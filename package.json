{"name": "<PERSON><PERSON>-media-player", "version": "0.4.7", "type": "module", "description": {"default": "Professional media player plugin for SiYuan Note with multi-platform support, smart note integration, timestamp jumps, loop segments, media assistant, and extensible script architecture for enhanced learning experience.", "zh_CN": "专业的思源笔记媒体播放插件，集成多平台播放、智能笔记管理、时间戳跳转、循环片段、媒体助手与可扩展脚本架构，打造完整的学习笔记生态。"}, "author": "mm-o", "license": "MIT", "url": "https://github.com/mm-o/siyuan-media-player", "scripts": {"dev": "cross-env NODE_ENV=development VITE_SOURCEMAP=inline vite build --watch", "build": "cross-env NODE_ENV=production vite build", "make-link": "node --no-warnings ./scripts/make_dev_link.js", "make-link-win": "powershell.exe -NoProfile -ExecutionPolicy Bypass -File ./scripts/elevate.ps1 -scriptPath ./scripts/make_dev_link.js", "update-version": "node --no-warnings ./scripts/update_version.js", "make-install": "vite build && node --no-warnings ./scripts/make_install.js"}, "minAppVersion": "2.10.13", "backends": ["windows", "linux", "darwin"], "frontends": ["desktop"], "displayName": {"default": "SiYuan Media Player", "zh_CN": "思源媒体播放器"}, "readme": {"default": "README.md", "zh_CN": "README_zh_CN.md"}, "funding": {"openCollective": "", "patreon": "", "github": "", "custom": []}, "keywords": ["media player", "audio", "video", "timestamp", "playlist", "note integration", "learning", "extensible", "multi-platform", "播放器", "音频", "视频", "时间戳", "播放列表", "笔记集成", "学习工具", "可扩展", "多平台"], "devDependencies": {"@sveltejs/vite-plugin-svelte": "^3.1.0", "@tsconfig/svelte": "^4.0.1", "@types/dplayer": "^1.25.5", "@types/node": "^20.3.0", "cross-env": "^7.0.3", "dashjs": "^5.0.0", "fast-glob": "^3.2.12", "glob": "^10.0.0", "js-yaml": "^4.1.0", "minimist": "^1.2.8", "rollup-plugin-livereload": "^2.0.5", "sass": "^1.63.3", "siyuan": "1.0.4", "svelte": "^4.2.19", "ts-node": "^10.9.1", "typescript": "^5.1.3", "vite": "^5.2.9", "vite-plugin-static-copy": "^1.0.2", "vite-plugin-zip-pack": "^1.0.5"}, "dependencies": {"@aws-sdk/client-s3": "^3.857.0", "@types/md5": "^2.3.5", "@types/node": "^20.2.5", "@types/qrcode": "^1.5.5", "artplayer": "^5.2.1", "artplayer-plugin-danmuku": "^5.1.6", "artplayer-plugin-dash-control": "^1.0.0", "browserify-zlib": "^0.2.0", "crypto-browserify": "^3.12.1", "md5": "^2.3.0", "qrcode": "^1.5.4", "viewerjs": "^1.11.7"}}