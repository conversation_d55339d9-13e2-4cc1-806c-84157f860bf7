/**
 * 极限精简许可证管理器 - 一步到位版
 * 极限精简，简洁高效，优雅完美
 */

import { S3Client, GetObjectCommand, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';

// 统一许可证接口
export interface LicenseInfo {
    type: 'trial' | 'annual' | 'dragon';
    userId: string;
    userName: string;
    activatedAt: number;
    expiresAt: number;      // 0 = 永久
    code?: string;          // 激活码（体验会员无）
    isValid: boolean;
    features: string[];
    lastCheck: number;      // 最后验证时间
}

export class LicenseManager {
    private static readonly LICENSE_FILE = 'license';
    private static readonly CHECK_INTERVAL = 24 * 60 * 60 * 1000; // 24小时

    // S3配置
    private static readonly S3_CONFIG = {
        accessKey: 'kFCOCF3QVYUcXHk75A5SC9VLPkYzVZZHPq2oApnk',
        secretKey: 'w5YsfJLlrJ3FJdafTq35ht5JYcc38svuDS3YI2Xp',
        bucket: 'siyuan-mediaplayer',
        region: 'cn-south-1',
        endpoint: 'https://s3.cn-south-1.qiniucs.com',
        encryptionKey: 'SiYuan_Fixed_Master_Key_2024_Secure_Change_This'
    };

    // 🎯 唯一入口 - 极限精简版
    static async activate(code: string = '', plugin: any): Promise<{
        success: boolean;
        license?: LicenseInfo;
        message?: string;
        error?: string;
    }> {
        try {
            // 1. 有激活码时，优先使用激活码
            if (code.trim()) {
                const license = await this.createWithCode(code.trim());
                await this.save(license, plugin);
                return {
                    success: true,
                    license,
                    message: '激活成功'
                };
            }

            // 2. 无激活码时，检查现有许可证
            const existing = await this.load(plugin);
            if (existing && this.isValid(existing)) {
                return { success: true, license: existing, message: '许可证有效' };
            }

            // 3. 清除无效许可证，创建体验会员
            if (existing) await this.clear(plugin);
            const license = await this.createTrial();
            await this.save(license, plugin);
            return {
                success: true,
                license,
                message: '体验会员激活成功'
            };

        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // 📖 加载许可证
    static async load(plugin: any): Promise<LicenseInfo | null> {
        try {
            const encrypted = await plugin.loadData(this.LICENSE_FILE);
            if (!encrypted || typeof encrypted !== 'string') return null;

            const decrypted = await this.decrypt(encrypted);
            const license: LicenseInfo = JSON.parse(decrypted);

            if (!this.verifySignature(license)) {
                await this.clear(plugin);
                return null;
            }

            return license;
        } catch (error) {
            await this.clear(plugin);
            return null;
        }
    }

    // ✅ 验证许可证有效性
    private static isValid(license: LicenseInfo): boolean {
        try {
            // 1. 检查过期
            if (license.expiresAt > 0 && license.expiresAt < Date.now()) {
                return false;
            }

            // 2. 检查是否需要在线验证
            if (license.type !== 'trial' && this.needsOnlineCheck(license)) {
                // 需要在线验证，暂时返回false，让系统重新激活
                return false;
            }

            return true;
        } catch (error) {
            console.warn('验证失败:', error.message);
            return false;
        }
    }

    // 🆕 创建体验会员
    private static async createTrial(): Promise<LicenseInfo> {
        const user = await this.getSiYuanUserInfo();
        return {
            type: 'trial',
            userId: user?.userId || 'guest',
            userName: user?.userName || '游客',
            activatedAt: Date.now(),
            expiresAt: Date.now() + (7 * 24 * 60 * 60 * 1000),
            isValid: true,
            features: ['basic_playback'],
            lastCheck: Date.now()
        };
    }

    // 🆕 使用激活码创建许可证
    private static async createWithCode(code: string): Promise<LicenseInfo> {
        const user = await this.getSiYuanUserInfo();
        if (!user) throw new Error('请先登录思源账号');

        const cleanUserName = user.userName.replace(/[^\w\u4e00-\u9fa5]/g, '');
        const userPath = `${code}_${user.userId}_${cleanUserName}.dat`;

        // 优先尝试原始激活码（首次激活）
        try {
            const originalData = await this.downloadS3(`${code}.dat`);
            const activatedAt = Date.now();
            const expiresAt = originalData.licenseType === 'dragon' ? 0 :
                             activatedAt + (365 * 24 * 60 * 60 * 1000);

            // 重命名S3文件
            await this.renameS3(`${code}.dat`, userPath, {
                licenseType: originalData.licenseType,
                activatedAt,
                expiresAt,
                features: this.getFeatures(originalData.licenseType)
            });

            return {
                type: originalData.licenseType,
                userId: user.userId,
                userName: user.userName,
                activatedAt,
                expiresAt,
                code: code,
                isValid: true,
                features: this.getFeatures(originalData.licenseType),
                lastCheck: Date.now()
            };
        } catch (error) {
            // 原始激活码不存在，尝试用户专属文件
        }

        // 尝试用户专属文件（重复激活）
        try {
            const data = await this.downloadS3(userPath);
            return {
                type: data.licenseType,
                userId: user.userId,
                userName: user.userName,
                activatedAt: data.activatedAt,
                expiresAt: data.expiresAt,
                code: code,
                isValid: true,
                features: this.getFeatures(data.licenseType),
                lastCheck: Date.now()
            };
        } catch (error) {
            throw new Error('激活码不存在或已被使用');
        }
    }

    // 💾 保存许可证
    private static async save(license: LicenseInfo, plugin: any): Promise<void> {
        const signature = this.generateSignature(license);
        const licenseWithSignature = { ...license, signature };
        const encrypted = await this.encrypt(JSON.stringify(licenseWithSignature));
        await plugin.saveData(this.LICENSE_FILE, encrypted, 2);
    }

    // 🗑️ 清除许可证
    static async clear(plugin: any): Promise<void> {
        await plugin.saveData(this.LICENSE_FILE, null, 2);
    }

    // 🔐 加密解密
    private static async encrypt(data: string): Promise<string> {
        const key = await crypto.subtle.digest('SHA-256', new TextEncoder().encode('siyuan-media-player'));
        const iv = crypto.getRandomValues(new Uint8Array(16));
        const keyObj = await crypto.subtle.importKey('raw', key, 'AES-GCM', false, ['encrypt']);
        const encrypted = await crypto.subtle.encrypt({ name: 'AES-GCM', iv }, keyObj, new TextEncoder().encode(data));

        const combined = new Uint8Array(iv.length + encrypted.byteLength);
        combined.set(iv);
        combined.set(new Uint8Array(encrypted), iv.length);
        return this.arrayBufferToBase64(combined);
    }

    private static async decrypt(encryptedData: string): Promise<string> {
        const combined = this.base64ToArrayBuffer(encryptedData);
        const iv = combined.slice(0, 16);
        const encrypted = combined.slice(16);

        const key = await crypto.subtle.digest('SHA-256', new TextEncoder().encode('siyuan-media-player'));
        const keyObj = await crypto.subtle.importKey('raw', key, 'AES-GCM', false, ['decrypt']);
        const decrypted = await crypto.subtle.decrypt({ name: 'AES-GCM', iv }, keyObj, encrypted);
        return new TextDecoder().decode(decrypted);
    }

    // Base64编码/解码
    private static arrayBufferToBase64(buffer: Uint8Array): string {
        let binary = '';
        for (let i = 0; i < buffer.length; i++) {
            binary += String.fromCharCode(buffer[i]);
        }
        return btoa(binary);
    }

    private static base64ToArrayBuffer(base64: string): Uint8Array {
        const cleanBase64 = base64.replace(/[^A-Za-z0-9+/=]/g, '');
        const binary = atob(cleanBase64);
        const bytes = new Uint8Array(binary.length);
        for (let i = 0; i < binary.length; i++) {
            bytes[i] = binary.charCodeAt(i);
        }
        return bytes;
    }

    // 🔍 工具函数
    private static needsOnlineCheck(license: LicenseInfo): boolean {
        return Date.now() - license.lastCheck > this.CHECK_INTERVAL;
    }

    private static generateSignature(license: LicenseInfo): string {
        const { isValid, ...data } = license;
        const hash = JSON.stringify(data) + 'salt';
        return btoa(hash).slice(0, 32);
    }

    private static verifySignature(license: any): boolean {
        if (!license.signature) return false;
        const { signature, ...data } = license;
        const expected = this.generateSignature(data);
        return expected === signature;
    }

    private static getFeatures(type: string): string[] {
        const features = {
            trial: ['basic_playback'],
            annual: ['cloud_sync', 'batch_import', 'standard_support'],
            dragon: ['cloud_sync', 'batch_import', 'priority_support', 'advanced_features']
        };
        return features[type] || features.trial;
    }

    // S3操作
    private static createS3Client(): S3Client {
        return new S3Client({
            region: this.S3_CONFIG.region,
            endpoint: this.S3_CONFIG.endpoint,
            credentials: {
                accessKeyId: this.S3_CONFIG.accessKey,
                secretAccessKey: this.S3_CONFIG.secretKey
            }
        });
    }



    private static async downloadS3(path: string): Promise<any> {
        try {
            const client = this.createS3Client();
            const command = new GetObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: path
            });
            const response = await client.send(command);
            const data = await response.Body?.transformToString();
            return await this.decryptS3Data(data!);
        } catch (error) {
            throw new Error(`下载S3文件失败: ${error.message}`);
        }
    }

    private static async renameS3(oldPath: string, newPath: string, data: any): Promise<void> {
        try {
            const client = this.createS3Client();
            const encryptedData = await this.encryptS3Data(data);

            // 上传新文件
            const putCommand = new PutObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: newPath,
                Body: encryptedData,
                ContentType: 'application/json'
            });
            await client.send(putCommand);

            // 删除原文件
            const deleteCommand = new DeleteObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: oldPath
            });
            await client.send(deleteCommand);
        } catch (error) {
            throw new Error(`重命名S3文件失败: ${error.message}`);
        }
    }

    private static async encryptS3Data(data: any): Promise<string> {
        const jsonData = JSON.stringify(data);
        const salt = crypto.getRandomValues(new Uint8Array(16));
        const iv = crypto.getRandomValues(new Uint8Array(16));

        const keyMaterial = await crypto.subtle.importKey(
            'raw',
            new TextEncoder().encode(this.S3_CONFIG.encryptionKey),
            'PBKDF2',
            false,
            ['deriveKey']
        );

        const key = await crypto.subtle.deriveKey(
            {
                name: 'PBKDF2',
                salt: salt,
                iterations: 10000,
                hash: 'SHA-256'
            },
            keyMaterial,
            { name: 'AES-CBC', length: 256 },
            false,
            ['encrypt']
        );

        const encrypted = await crypto.subtle.encrypt(
            { name: 'AES-CBC', iv: iv },
            key,
            new TextEncoder().encode(jsonData)
        );

        return JSON.stringify({
            data: btoa(String.fromCharCode(...new Uint8Array(encrypted))),
            salt: btoa(String.fromCharCode(...salt)),
            iv: btoa(String.fromCharCode(...iv))
        });
    }

    private static async decryptS3Data(encryptedDataStr: string): Promise<any> {
        try {
            const encryptedData = JSON.parse(encryptedDataStr);
            const salt = Uint8Array.from(atob(encryptedData.salt), c => c.charCodeAt(0));
            const iv = Uint8Array.from(atob(encryptedData.iv), c => c.charCodeAt(0));

            const keyMaterial = await crypto.subtle.importKey(
                'raw',
                new TextEncoder().encode(this.S3_CONFIG.encryptionKey),
                'PBKDF2',
                false,
                ['deriveKey']
            );

            const key = await crypto.subtle.deriveKey(
                {
                    name: 'PBKDF2',
                    salt: salt,
                    iterations: 10000,
                    hash: 'SHA-256'
                },
                keyMaterial,
                { name: 'AES-CBC', length: 256 },
                false,
                ['decrypt']
            );

            const encryptedBytes = Uint8Array.from(atob(encryptedData.data), c => c.charCodeAt(0));
            const decrypted = await crypto.subtle.decrypt(
                { name: 'AES-CBC', iv: iv },
                key,
                encryptedBytes
            );

            // 处理gzip解压缩
            try {
                const decompressedStream = new DecompressionStream('gzip');
                const writer = decompressedStream.writable.getWriter();
                const reader = decompressedStream.readable.getReader();

                writer.write(new Uint8Array(decrypted));
                writer.close();

                const chunks = [];
                let done = false;
                while (!done) {
                    const { value, done: readerDone } = await reader.read();
                    done = readerDone;
                    if (value) chunks.push(value);
                }

                const decompressed = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));
                let offset = 0;
                for (const chunk of chunks) {
                    decompressed.set(chunk, offset);
                    offset += chunk.length;
                }

                const decryptedText = new TextDecoder().decode(decompressed);
                return JSON.parse(decryptedText);
            } catch (gzipError) {
                const decryptedText = new TextDecoder().decode(decrypted);
                return JSON.parse(decryptedText);
            }
        } catch (error) {
            throw new Error(`S3数据解密失败: ${error.message}`);
        }
    }

    // 🔍 获取思源用户信息
    static async getSiYuanUserInfo(): Promise<{ userId: string; userName: string } | null> {
        try {
            // 优先使用全局对象
            if ((window as any).siyuan?.user?.userId) {
                return {
                    userId: (window as any).siyuan.user.userId,
                    userName: (window as any).siyuan.user.userName || 'Unknown'
                };
            }

            // 备用API方式
            const response = await fetch('/api/system/getConf');
            if (response.status === 200) {
                const text = await response.text();
                if (text.trim()) {
                    const data = JSON.parse(text);
                    if (data.code === 0 && data.data?.user) {
                        return {
                            userId: data.data.user.userId,
                            userName: data.data.user.userName
                        };
                    }
                }
            }

            return null;
        } catch (error) {
            return null;
        }
    }

}
