/**
 * 许可证管理器 - 集成S3验证版
 */

import { S3Client, GetObjectCommand, CopyObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';

export interface LicenseInfo {
    code: string;
    userId: string;
    userName: string;
    activatedAt: number;
    expiresAt: number;
    type: 'dragon' | 'annual' | 'trial';
    isValid: boolean;
    features: string[];
}

// 本地许可证结构
interface LocalLicense {
    originalCode: string;
    userLicenseCode: string;
    userId: string;
    userName: string;
    licenseType: 'dragon' | 'annual' | 'trial';
    activatedAt: number;
    expiresAt: number;
    features: string[];
    signature: string;
}

export class LicenseManager {
    private static readonly TRIAL_DURATION = 7 * 24 * 60 * 60 * 1000;

    // S3配置
    private static readonly S3_CONFIG = {
        accessKey: 'kFCOCF3QVYUcXHk75A5SC9VLPkYzVZZHPq2oApnk',
        secretKey: 'w5YsfJLlrJ3FJdafTq35ht5JYcc38svuDS3YI2Xp',
        bucket: 'siyuan-mediaplayer',
        region: 'cn-south-1',
        endpoint: 'https://s3.cn-south-1.qiniucs.com',
        encryptionKey: 'SiYuan_Fixed_Master_Key_2024_Secure_Change_This'
    };

    private static getFeaturesForType(type: string): string[] {
        const features = {
            dragon: ['unlimited_cloud', 'batch_import', 'priority_support', 'advanced_features'],
            annual: ['cloud_sync', 'batch_import', 'standard_support'],
            trial: ['basic_features']
        };
        return features[type] || features.trial;
    }

    // 创建S3客户端
    private static createS3Client() {
        return new S3Client({
            region: this.S3_CONFIG.region,
            endpoint: this.S3_CONFIG.endpoint,
            credentials: {
                accessKeyId: this.S3_CONFIG.accessKey,
                secretAccessKey: this.S3_CONFIG.secretKey
            },
            forcePathStyle: true // 七牛云需要使用path-style
        });
    }

    // S3下载文件
    private static async downloadFromS3(key: string): Promise<string> {
        try {
            const client = this.createS3Client();

            const command = new GetObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: key
            });

            const result = await client.send(command);

            // 读取流数据 - 参考tools/s3-client.js的实现
            if (result.Body) {
                const chunks = [];
                for await (const chunk of result.Body as any) {
                    chunks.push(chunk);
                }
                return Buffer.concat(chunks).toString('utf-8');
            }

            throw new Error('响应体为空');
        } catch (error) {
            throw new Error(`S3下载失败: ${error.message}`);
        }
    }

    // S3重命名文件（通过复制+删除实现）
    private static async renameS3File(oldKey: string, newKey: string): Promise<void> {
        try {
            const client = this.createS3Client();

            // 1. 复制文件到新位置
            const copyCommand = new CopyObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                CopySource: `${this.S3_CONFIG.bucket}/${oldKey}`,
                Key: newKey
            });

            await client.send(copyCommand);

            // 2. 删除原文件
            const deleteCommand = new DeleteObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: oldKey
            });

            await client.send(deleteCommand);
        } catch (error) {
            throw new Error(`S3重命名失败: ${error.message}`);
        }
    }

    // 解密数据包 - 简化版本，处理gzip压缩
    private static async decryptData(encryptedDataStr: string): Promise<any> {
        try {
            const encryptedData = JSON.parse(encryptedDataStr);

            // 使用Web Crypto API解密
            const salt = Uint8Array.from(atob(encryptedData.salt), c => c.charCodeAt(0));
            const iv = Uint8Array.from(atob(encryptedData.iv), c => c.charCodeAt(0));

            // 生成密钥
            const keyMaterial = await crypto.subtle.importKey(
                'raw',
                new TextEncoder().encode(this.S3_CONFIG.encryptionKey),
                'PBKDF2',
                false,
                ['deriveKey']
            );

            const key = await crypto.subtle.deriveKey(
                {
                    name: 'PBKDF2',
                    salt: salt,
                    iterations: 10000,
                    hash: 'SHA-256'
                },
                keyMaterial,
                { name: 'AES-CBC', length: 256 },
                false,
                ['decrypt']
            );

            // 解密数据
            const encryptedBytes = Uint8Array.from(atob(encryptedData.data), c => c.charCodeAt(0));
            const decrypted = await crypto.subtle.decrypt(
                { name: 'AES-CBC', iv: iv },
                key,
                encryptedBytes
            );

            // 处理gzip解压缩 - 使用DecompressionStream API
            try {
                const decompressedStream = new DecompressionStream('gzip');
                const writer = decompressedStream.writable.getWriter();
                const reader = decompressedStream.readable.getReader();

                writer.write(new Uint8Array(decrypted));
                writer.close();

                const chunks = [];
                let done = false;
                while (!done) {
                    const { value, done: readerDone } = await reader.read();
                    done = readerDone;
                    if (value) chunks.push(value);
                }

                const decompressed = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));
                let offset = 0;
                for (const chunk of chunks) {
                    decompressed.set(chunk, offset);
                    offset += chunk.length;
                }

                const decryptedText = new TextDecoder().decode(decompressed);
                return JSON.parse(decryptedText);
            } catch (gzipError) {
                // 如果gzip解压失败，尝试直接解析（可能没有压缩）
                const decryptedText = new TextDecoder().decode(decrypted);
                return JSON.parse(decryptedText);
            }
        } catch (error) {
            throw new Error(`解密失败: ${error.message}`);
        }
    }



    // 检查S3文件是否存在
    private static async checkS3FileExists(key: string): Promise<boolean> {
        try {
            const client = this.createS3Client();
            const command = new GetObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: key
            });
            await client.send(command);
            return true;
        } catch (error) {
            return false;
        }
    }

    // 生成用户专属激活码（不含扩展名）
    private static generateUserLicenseCode(code: string, user: { userId: string; userName: string }): string {
        const cleanUserName = user.userName.replace(/[^a-zA-Z0-9]/g, '');
        return `${code}_${user.userId}_${cleanUserName}`;
    }

    // 生成用户专属激活码文件名（含扩展名）
    private static generateUserLicenseKey(code: string, user: { userId: string; userName: string }): string {
        return `${this.generateUserLicenseCode(code, user)}.dat`;
    }

    // 生成许可证签名（防篡改）
    private static generateLicenseSignature(license: Omit<LocalLicense, 'signature'>): string {
        const data = JSON.stringify(license);
        const key = this.S3_CONFIG.encryptionKey;
        // 简化签名：使用固定密钥 + 数据的哈希
        return btoa(data + key).slice(0, 32);
    }

    // 验证许可证签名
    private static verifyLicenseSignature(license: LocalLicense): boolean {
        const { signature, ...licenseData } = license;
        const expectedSignature = this.generateLicenseSignature(licenseData);
        return signature === expectedSignature;
    }

    // 保存本地许可证
    private static async saveLocalLicense(license: LocalLicense, plugin: any): Promise<void> {
        try {
            await plugin.saveData('license.json', license, 2);
        } catch (error) {
            console.warn('保存本地许可证失败:', error);
        }
    }

    // 读取本地许可证
    private static async loadLocalLicense(plugin: any): Promise<LocalLicense | null> {
        try {
            const license = await plugin.loadData('license.json');
            if (!license) return null;

            // 验证签名
            if (!this.verifyLicenseSignature(license)) {
                console.warn('本地许可证签名验证失败');
                await plugin.saveData('license.json', null, 2); // 清除无效许可证
                return null;
            }

            return license;
        } catch (error) {
            console.warn('读取本地许可证失败:', error);
            return null;
        }
    }

    // 验证激活码 - 智能版（首次激活vs后续验证）
    static async validateLicense(code: string, plugin?: any): Promise<{ success: boolean; data?: LicenseInfo; error?: string; userLicenseCode?: string }> {
        if (!code) return { success: false, error: '激活码不能为空' };

        try {
            // 获取用户信息
            const user = await this.getSiYuanUserInfo();
            if (!user) return { success: false, error: '请先登录思源账号' };

            // 生成用户专属激活码和文件名
            const userLicenseCode = this.generateUserLicenseCode(code, user);
            const userLicenseKey = this.generateUserLicenseKey(code, user);

            // 检查是否已经是用户专属激活码（包含用户信息）
            const isUserSpecificCode = code.includes(`_${user.userId}_`);

            if (isUserSpecificCode) {
                // 情况1：已经是用户专属激活码，使用本地许可证
                if (!plugin) {
                    return { success: false, error: '插件实例未提供，无法读取本地许可证' };
                }

                const localLicense = await this.loadLocalLicense(plugin);

                if (!localLicense || localLicense.userLicenseCode !== code) {
                    return { success: false, error: '本地许可证不存在或不匹配' };
                }

                // 检查S3文件是否仍然存在（在线验证）
                const fileKey = `${code}.dat`;
                const exists = await this.checkS3FileExists(fileKey);
                if (!exists) {
                    return { success: false, error: '激活码已失效或不存在' };
                }

                // 使用本地许可证的准确信息
                const licenseInfo: LicenseInfo = {
                    code: localLicense.originalCode,
                    userId: localLicense.userId,
                    userName: localLicense.userName,
                    activatedAt: localLicense.activatedAt,
                    expiresAt: localLicense.expiresAt,
                    type: localLicense.licenseType,
                    isValid: true,
                    features: localLicense.features
                };

                return { success: true, data: licenseInfo, userLicenseCode: code };
            } else {
                // 情况2：首次激活，需要完整流程

                // 检查用户专属文件是否已存在（防止重复激活）
                const userFileExists = await this.checkS3FileExists(userLicenseKey);
                if (userFileExists) {
                    return { success: false, error: '该激活码已被当前用户使用' };
                }

                // 下载原始激活码数据包
                const encryptedData = await this.downloadFromS3(`${code}.dat`);

                // 解密数据包进行验证
                const licenseData = await this.decryptData(encryptedData);

                // 验证许可证有效性
                if (!licenseData || !licenseData.licenseType) {
                    return { success: false, error: '激活码数据无效' };
                }

                // 重命名文件标记已使用
                await this.renameS3File(`${code}.dat`, userLicenseKey);

                // 构建许可证信息
                const activatedAt = Date.now();
                const expiresAt = licenseData.licenseType === 'dragon' ? 0 : activatedAt + (365 * 24 * 60 * 60 * 1000);
                const features = this.getFeaturesForType(licenseData.licenseType);

                const licenseInfo: LicenseInfo = {
                    code,
                    userId: user.userId,
                    userName: user.userName,
                    activatedAt,
                    expiresAt,
                    type: licenseData.licenseType,
                    isValid: true,
                    features
                };

                // 创建并保存本地许可证
                const localLicense: LocalLicense = {
                    originalCode: code,
                    userLicenseCode: userLicenseCode,
                    userId: user.userId,
                    userName: user.userName,
                    licenseType: licenseData.licenseType,
                    activatedAt,
                    expiresAt,
                    features,
                    signature: '' // 临时占位
                };

                // 生成签名
                localLicense.signature = this.generateLicenseSignature(localLicense);

                // 保存到本地
                if (plugin) {
                    await this.saveLocalLicense(localLicense, plugin);
                }

                return { success: true, data: licenseInfo, userLicenseCode: userLicenseCode };
            }
        } catch (error) {
            return { success: false, error: `激活码验证失败: ${error.message}` };
        }
    }

    static createTrialLicense(user: { userId: string; userName: string }): LicenseInfo {
        const now = Date.now();
        return {
            code: `TRIAL_${user.userId}_${now}`,
            userId: user.userId,
            userName: user.userName,
            activatedAt: now,
            expiresAt: now + this.TRIAL_DURATION,
            type: 'trial',
            isValid: true,
            features: this.getFeaturesForType('trial')
        };
    }

    // 检查许可证是否过期
    static isLicenseExpired(license: LicenseInfo): boolean {
        if (license.type === 'dragon') return false;
        return license.expiresAt > 0 && license.expiresAt < Date.now();
    }

    // 获取许可证剩余天数
    static getRemainingDays(license: LicenseInfo): number {
        if (license.type === 'dragon') return -1;
        const remaining = license.expiresAt - Date.now();
        return Math.max(0, Math.ceil(remaining / (24 * 60 * 60 * 1000)));
    }

    // 获取思源用户信息
    static async getSiYuanUserInfo(): Promise<{ userId: string; userName: string } | null> {
        try {
            // 优先使用全局对象
            if (typeof window !== 'undefined' && (window as any).siyuan?.user?.userId) {
                const user = (window as any).siyuan.user;
                return {
                    userId: user.userId,
                    userName: user.userName || user.userNickname || '思源用户'
                };
            }

            // 回退到API方式
            const response = await fetch('/api/system/getConf', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: '{}'
            });

            const data = await response.json();
            const user = data?.data?.conf?.user;

            return user?.userId ? {
                userId: user.userId,
                userName: user.userName || user.userNickname || '思源用户'
            } : null;
        } catch {
            return null;
        }
    }

    // 检查许可证是否有效
    static isLicenseValid(license: LicenseInfo | null): boolean {
        return !!(license && license.isValid && !this.isLicenseExpired(license));
    }

    // 获取当前许可证信息
    static async getCurrentLicense(licenseCode: string | null, plugin?: any): Promise<LicenseInfo | null> {
        if (!licenseCode) return null;
        const result = await this.validateLicense(licenseCode, plugin);
        return result.success ? result.data || null : null;
    }

    // 获取会员类型信息
    static getMemberTypeInfo(type: string): { icon: string; name: string; color: string } {
        const typeMap = {
            dragon: { icon: '#iconDragon', name: '恶龙会员', color: '#ff6b35' },
            annual: { icon: '#iconVIP', name: '年度会员', color: '#4facfe' },
            trial: { icon: '#iconClock', name: '体验会员', color: '#95de64' }
        };
        return typeMap[type] || typeMap.trial;
    }

    // 处理Pro状态变更
    static async handleProState(
        enabled: boolean,
        licenseCode: string,
        currentLicenseCode: string | null,
        plugin?: any
    ): Promise<{ success: boolean; message?: string; error?: string; license?: LicenseInfo; newLicenseCode?: string }> {
        if (!enabled) {
            return { success: true, message: 'Pro功能已停用', license: null };
        }

        if (licenseCode) {
            // 使用激活码激活
            const result = await this.validateLicense(licenseCode, plugin);
            return result.success && result.data ? {
                success: true,
                message: '激活成功！',
                license: result.data,
                newLicenseCode: result.userLicenseCode || licenseCode // 保存用户专属激活码
            } : {
                success: false,
                error: result.error || '激活码验证失败'
            };
        }

        // 创建7天体验账号
        const user = await this.getSiYuanUserInfo();
        if (!user) {
            return { success: false, error: '请先登录思源账号' };
        }

        // 检查现有体验许可证
        if (currentLicenseCode) {
            const existingLicense = await this.getCurrentLicense(currentLicenseCode);
            if (existingLicense && existingLicense.type === 'trial' && !this.isLicenseExpired(existingLicense)) {
                return { success: true, message: '体验账号仍然有效', license: existingLicense };
            }
        }

        // 创建新体验许可证
        const trialLicense = this.createTrialLicense(user);
        return {
            success: true,
            message: '7天体验账号已开启！',
            license: trialLicense,
            newLicenseCode: trialLicense.code
        };
    }
}
