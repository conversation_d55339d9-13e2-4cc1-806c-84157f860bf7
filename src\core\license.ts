/**
 * 许可证管理器 - 集成S3验证版
 */

import { S3Client, GetObjectCommand, CopyObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';

export interface LicenseInfo {
    code: string;
    userId: string;
    userName: string;
    activatedAt: number;
    expiresAt: number;
    type: 'dragon' | 'annual' | 'trial';
    isValid: boolean;
    features: string[];
}

export class LicenseManager {
    private static readonly TRIAL_DURATION = 7 * 24 * 60 * 60 * 1000;

    // S3配置
    private static readonly S3_CONFIG = {
        accessKey: 'kFCOCF3QVYUcXHk75A5SC9VLPkYzVZZHPq2oApnk',
        secretKey: 'w5YsfJLlrJ3FJdafTq35ht5JYcc38svuDS3YI2Xp',
        bucket: 'siyuan-mediaplayer',
        region: 'cn-south-1',
        endpoint: 'https://s3.cn-south-1.qiniucs.com',
        encryptionKey: 'SiYuan_Fixed_Master_Key_2024_Secure_Change_This'
    };

    private static getFeaturesForType(type: string): string[] {
        const features = {
            dragon: ['unlimited_cloud', 'batch_import', 'priority_support', 'advanced_features'],
            annual: ['cloud_sync', 'batch_import', 'standard_support'],
            trial: ['basic_features']
        };
        return features[type] || features.trial;
    }

    // 创建S3客户端
    private static createS3Client() {
        return new S3Client({
            region: this.S3_CONFIG.region,
            endpoint: this.S3_CONFIG.endpoint,
            credentials: {
                accessKeyId: this.S3_CONFIG.accessKey,
                secretAccessKey: this.S3_CONFIG.secretKey
            },
            forcePathStyle: true // 七牛云需要使用path-style
        });
    }

    // S3下载文件
    private static async downloadFromS3(key: string): Promise<string> {
        try {
            const client = this.createS3Client();

            const command = new GetObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: key
            });

            const result = await client.send(command);

            // 读取流数据 - 参考tools/s3-client.js的实现
            if (result.Body) {
                const chunks = [];
                for await (const chunk of result.Body as any) {
                    chunks.push(chunk);
                }
                return Buffer.concat(chunks).toString('utf-8');
            }

            throw new Error('响应体为空');
        } catch (error) {
            throw new Error(`S3下载失败: ${error.message}`);
        }
    }

    // S3重命名文件（通过复制+删除实现）
    private static async renameS3File(oldKey: string, newKey: string): Promise<void> {
        try {
            const client = this.createS3Client();

            // 1. 复制文件到新位置
            const copyCommand = new CopyObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                CopySource: `${this.S3_CONFIG.bucket}/${oldKey}`,
                Key: newKey
            });

            await client.send(copyCommand);

            // 2. 删除原文件
            const deleteCommand = new DeleteObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: oldKey
            });

            await client.send(deleteCommand);
        } catch (error) {
            throw new Error(`S3重命名失败: ${error.message}`);
        }
    }

    // 解密数据包 - 简化版本，处理gzip压缩
    private static async decryptData(encryptedDataStr: string): Promise<any> {
        try {
            const encryptedData = JSON.parse(encryptedDataStr);

            // 使用Web Crypto API解密
            const salt = Uint8Array.from(atob(encryptedData.salt), c => c.charCodeAt(0));
            const iv = Uint8Array.from(atob(encryptedData.iv), c => c.charCodeAt(0));

            // 生成密钥
            const keyMaterial = await crypto.subtle.importKey(
                'raw',
                new TextEncoder().encode(this.S3_CONFIG.encryptionKey),
                'PBKDF2',
                false,
                ['deriveKey']
            );

            const key = await crypto.subtle.deriveKey(
                {
                    name: 'PBKDF2',
                    salt: salt,
                    iterations: 10000,
                    hash: 'SHA-256'
                },
                keyMaterial,
                { name: 'AES-CBC', length: 256 },
                false,
                ['decrypt']
            );

            // 解密数据
            const encryptedBytes = Uint8Array.from(atob(encryptedData.data), c => c.charCodeAt(0));
            const decrypted = await crypto.subtle.decrypt(
                { name: 'AES-CBC', iv: iv },
                key,
                encryptedBytes
            );

            // 处理gzip解压缩 - 使用DecompressionStream API
            try {
                const decompressedStream = new DecompressionStream('gzip');
                const writer = decompressedStream.writable.getWriter();
                const reader = decompressedStream.readable.getReader();

                writer.write(new Uint8Array(decrypted));
                writer.close();

                const chunks = [];
                let done = false;
                while (!done) {
                    const { value, done: readerDone } = await reader.read();
                    done = readerDone;
                    if (value) chunks.push(value);
                }

                const decompressed = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));
                let offset = 0;
                for (const chunk of chunks) {
                    decompressed.set(chunk, offset);
                    offset += chunk.length;
                }

                const decryptedText = new TextDecoder().decode(decompressed);
                return JSON.parse(decryptedText);
            } catch (gzipError) {
                // 如果gzip解压失败，尝试直接解析（可能没有压缩）
                const decryptedText = new TextDecoder().decode(decrypted);
                return JSON.parse(decryptedText);
            }
        } catch (error) {
            throw new Error(`解密失败: ${error.message}`);
        }
    }



    // 检查S3文件是否存在
    private static async checkS3FileExists(key: string): Promise<boolean> {
        try {
            const client = this.createS3Client();
            const command = new GetObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: key
            });
            await client.send(command);
            return true;
        } catch (error) {
            return false;
        }
    }

    // 类型映射
    private static readonly TYPE_MAP = { dragon: 'D', annual: 'A', trial: 'T' };
    private static readonly TYPE_REVERSE_MAP = { D: 'dragon', A: 'annual', T: 'trial' };

    // 基准时间戳（2024-01-01）
    private static readonly BASE_TIMESTAMP = 1704067200000;

    // 压缩时间戳（精确到天，Base36编码）
    private static compressTimestamp(timestamp: number): string {
        const days = Math.floor((timestamp - this.BASE_TIMESTAMP) / (24 * 60 * 60 * 1000));
        return days.toString(36).toUpperCase().padStart(4, '0');
    }

    // 解压时间戳
    private static decompressTimestamp(compressed: string): number {
        const days = parseInt(compressed, 36);
        return this.BASE_TIMESTAMP + (days * 24 * 60 * 60 * 1000);
    }

    // 编码用户专属激活码（包含类型和时间戳）
    private static encodeUserLicenseCode(
        originalCode: string,
        user: { userId: string; userName: string },
        licenseType: string,
        expiresAt: number
    ): string {
        const cleanUserName = user.userName.replace(/[^a-zA-Z0-9]/g, '');
        const typeChar = this.TYPE_MAP[licenseType] || 'T';
        const compressedTime = this.compressTimestamp(expiresAt);

        return `${originalCode}_${user.userId}_${cleanUserName}_${typeChar}${compressedTime}`;
    }

    // 解码用户专属激活码
    private static decodeUserLicenseCode(encodedCode: string): {
        originalCode: string;
        userId: string;
        userName: string;
        licenseType: string;
        expiresAt: number;
    } | null {
        try {
            const parts = encodedCode.split('_');
            if (parts.length < 4) return null;

            const originalCode = parts[0];
            const userId = parts[1];
            const userName = parts[2];
            const typeAndTime = parts[3];

            if (typeAndTime.length < 5) return null;

            const typeChar = typeAndTime[0];
            const compressedTime = typeAndTime.slice(1);

            const licenseType = this.TYPE_REVERSE_MAP[typeChar];
            if (!licenseType) return null;

            const expiresAt = this.decompressTimestamp(compressedTime);

            return {
                originalCode,
                userId,
                userName,
                licenseType,
                expiresAt
            };
        } catch (error) {
            return null;
        }
    }

    // 生成查询用的文件名（剔除类型和时间戳）
    private static generateQueryFileName(encodedCode: string): string | null {
        const decoded = this.decodeUserLicenseCode(encodedCode);
        if (!decoded) return null;

        return `${decoded.originalCode}_${decoded.userId}_${decoded.userName}.dat`;
    }

    // 验证激活码 - 双重验证版（用户信息验证 + S3文件验证）
    static async validateLicense(code: string): Promise<{ success: boolean; data?: LicenseInfo; error?: string; userLicenseCode?: string }> {
        if (!code) return { success: false, error: '激活码不能为空' };

        try {
            // 获取当前用户信息
            const currentUser = await this.getSiYuanUserInfo();
            if (!currentUser) return { success: false, error: '请先登录思源账号' };

            // 检查是否已经是用户专属激活码（包含编码信息）
            const isUserSpecificCode = code.includes('_') && code.split('_').length >= 4;

            if (isUserSpecificCode) {
                // 情况1：用户专属激活码，进行双重验证

                // 第一重验证：解码并验证用户信息
                const decoded = this.decodeUserLicenseCode(code);
                if (!decoded) {
                    return { success: false, error: '激活码格式错误' };
                }

                // 验证用户信息是否一致
                if (decoded.userId !== currentUser.userId || decoded.userName !== currentUser.userName) {
                    return {
                        success: false,
                        error: `激活码绑定的用户信息不匹配\n绑定用户：${decoded.userName}(${decoded.userId})\n当前用户：${currentUser.userName}(${currentUser.userId})`
                    };
                }

                // 检查许可证是否过期
                if (decoded.licenseType !== 'dragon' && decoded.expiresAt < Date.now()) {
                    return { success: false, error: '激活码已过期' };
                }

                // 第二重验证：S3文件存在性验证
                const queryFileName = this.generateQueryFileName(code);
                if (!queryFileName) {
                    return { success: false, error: '激活码解析失败' };
                }

                const fileExists = await this.checkS3FileExists(queryFileName);
                if (!fileExists) {
                    return { success: false, error: '激活码已失效或被撤销' };
                }

                // 构建许可证信息
                const licenseInfo: LicenseInfo = {
                    code: decoded.originalCode,
                    userId: decoded.userId,
                    userName: decoded.userName,
                    activatedAt: Date.now(), // 可以从文件名或其他地方获取
                    expiresAt: decoded.expiresAt,
                    type: decoded.licenseType as any,
                    isValid: true,
                    features: this.getFeaturesForType(decoded.licenseType)
                };

                return { success: true, data: licenseInfo, userLicenseCode: code };

            } else {
                // 情况2：首次激活，需要完整流程

                // 下载原始激活码数据包
                const encryptedData = await this.downloadFromS3(`${code}.dat`);

                // 解密数据包进行验证
                const licenseData = await this.decryptData(encryptedData);

                // 验证许可证有效性
                if (!licenseData || !licenseData.licenseType) {
                    return { success: false, error: '激活码数据无效' };
                }

                // 计算过期时间
                const expiresAt = licenseData.licenseType === 'dragon' ? 0 : Date.now() + (365 * 24 * 60 * 60 * 1000);

                // 生成用户专属激活码（包含类型和时间戳）
                const userLicenseCode = this.encodeUserLicenseCode(code, currentUser, licenseData.licenseType, expiresAt);

                // 生成文件名（不含类型和时间戳）
                const userFileName = `${code}_${currentUser.userId}_${currentUser.userName.replace(/[^a-zA-Z0-9]/g, '')}.dat`;

                // 检查用户专属文件是否已存在（防止重复激活）
                const userFileExists = await this.checkS3FileExists(userFileName);
                if (userFileExists) {
                    return { success: false, error: '该激活码已被当前用户使用' };
                }

                // 重命名文件标记已使用
                await this.renameS3File(`${code}.dat`, userFileName);

                // 构建许可证信息
                const licenseInfo: LicenseInfo = {
                    code,
                    userId: currentUser.userId,
                    userName: currentUser.userName,
                    activatedAt: Date.now(),
                    expiresAt,
                    type: licenseData.licenseType,
                    isValid: true,
                    features: this.getFeaturesForType(licenseData.licenseType)
                };

                return { success: true, data: licenseInfo, userLicenseCode };
            }
        } catch (error) {
            return { success: false, error: `激活码验证失败: ${error.message}` };
        }
    }

    static createTrialLicense(user: { userId: string; userName: string }): LicenseInfo {
        const now = Date.now();
        return {
            code: `TRIAL_${user.userId}_${now}`,
            userId: user.userId,
            userName: user.userName,
            activatedAt: now,
            expiresAt: now + this.TRIAL_DURATION,
            type: 'trial',
            isValid: true,
            features: this.getFeaturesForType('trial')
        };
    }

    // 检查许可证是否过期
    static isLicenseExpired(license: LicenseInfo): boolean {
        if (license.type === 'dragon') return false;
        return license.expiresAt > 0 && license.expiresAt < Date.now();
    }

    // 获取许可证剩余天数
    static getRemainingDays(license: LicenseInfo): number {
        if (license.type === 'dragon') return -1;
        const remaining = license.expiresAt - Date.now();
        return Math.max(0, Math.ceil(remaining / (24 * 60 * 60 * 1000)));
    }

    // 获取思源用户信息
    static async getSiYuanUserInfo(): Promise<{ userId: string; userName: string } | null> {
        try {
            // 优先使用全局对象
            if (typeof window !== 'undefined' && (window as any).siyuan?.user?.userId) {
                const user = (window as any).siyuan.user;
                return {
                    userId: user.userId,
                    userName: user.userName || user.userNickname || '思源用户'
                };
            }

            // 回退到API方式
            const response = await fetch('/api/system/getConf', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: '{}'
            });

            const data = await response.json();
            const user = data?.data?.conf?.user;

            return user?.userId ? {
                userId: user.userId,
                userName: user.userName || user.userNickname || '思源用户'
            } : null;
        } catch {
            return null;
        }
    }

    // 检查许可证是否有效
    static isLicenseValid(license: LicenseInfo | null): boolean {
        return !!(license && license.isValid && !this.isLicenseExpired(license));
    }

    // 获取当前许可证信息
    static async getCurrentLicense(licenseCode: string | null): Promise<LicenseInfo | null> {
        if (!licenseCode) return null;
        const result = await this.validateLicense(licenseCode);
        return result.success ? result.data || null : null;
    }

    // 获取会员类型信息
    static getMemberTypeInfo(type: string): { icon: string; name: string; color: string } {
        const typeMap = {
            dragon: { icon: '#iconDragon', name: '恶龙会员', color: '#ff6b35' },
            annual: { icon: '#iconVIP', name: '年度会员', color: '#4facfe' },
            trial: { icon: '#iconClock', name: '体验会员', color: '#95de64' }
        };
        return typeMap[type] || typeMap.trial;
    }

    // 处理Pro状态变更
    static async handleProState(
        enabled: boolean,
        licenseCode: string,
        currentLicenseCode: string | null
    ): Promise<{ success: boolean; message?: string; error?: string; license?: LicenseInfo; newLicenseCode?: string }> {
        if (!enabled) {
            return { success: true, message: 'Pro功能已停用', license: null };
        }

        if (licenseCode) {
            // 使用激活码激活
            const result = await this.validateLicense(licenseCode);
            return result.success && result.data ? {
                success: true,
                message: '激活成功！',
                license: result.data,
                newLicenseCode: result.userLicenseCode || licenseCode // 保存用户专属激活码
            } : {
                success: false,
                error: result.error || '激活码验证失败'
            };
        }

        // 创建7天体验账号
        const user = await this.getSiYuanUserInfo();
        if (!user) {
            return { success: false, error: '请先登录思源账号' };
        }

        // 检查现有体验许可证
        if (currentLicenseCode) {
            const existingLicense = await this.getCurrentLicense(currentLicenseCode);
            if (existingLicense && existingLicense.type === 'trial' && !this.isLicenseExpired(existingLicense)) {
                return { success: true, message: '体验账号仍然有效', license: existingLicense };
            }
        }

        // 创建新体验许可证
        const trialLicense = this.createTrialLicense(user);
        return {
            success: true,
            message: '7天体验账号已开启！',
            license: trialLicense,
            newLicenseCode: trialLicense.code
        };
    }
}
