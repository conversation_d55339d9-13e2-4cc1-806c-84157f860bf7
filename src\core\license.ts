/**
 * 许可证管理器 - 集成S3验证版
 */

export interface LicenseInfo {
    code: string;
    userId: string;
    userName: string;
    activatedAt: number;
    expiresAt: number;
    type: 'dragon' | 'annual' | 'trial';
    isValid: boolean;
    features: string[];
}

export class LicenseManager {
    private static readonly TRIAL_DURATION = 7 * 24 * 60 * 60 * 1000;

    // S3配置
    private static readonly S3_CONFIG = {
        accessKey: 'kFCOCF3QVYUcXHk75A5SC9VLPkYzVZZHPq2oApnk',
        secretKey: 'w5YsfJLlrJ3FJdafTq35ht5JYcc38svuDS3YI2Xp',
        bucket: 'siyuan-mediaplayer',
        region: 'cn-south-1',
        endpoint: 'https://s3.cn-south-1.qiniucs.com',
        encryptionKey: 'SiYuan_Fixed_Master_Key_2024_Secure_Change_This'
    };

    private static getFeaturesForType(type: string): string[] {
        const features = {
            dragon: ['unlimited_cloud', 'batch_import', 'priority_support', 'advanced_features'],
            annual: ['cloud_sync', 'batch_import', 'standard_support'],
            trial: ['basic_features']
        };
        return features[type] || features.trial;
    }

    // 创建S3客户端
    private static async createS3Client() {
        try {
            // 动态导入AWS SDK
            const { S3Client } = await import('@aws-sdk/client-s3');

            return new S3Client({
                region: this.S3_CONFIG.region,
                endpoint: this.S3_CONFIG.endpoint,
                credentials: {
                    accessKeyId: this.S3_CONFIG.accessKey,
                    secretAccessKey: this.S3_CONFIG.secretKey
                },
                forcePathStyle: true // 七牛云需要使用path-style
            });
        } catch (error) {
            throw new Error('AWS SDK未安装，请安装@aws-sdk/client-s3');
        }
    }

    // S3下载文件
    private static async downloadFromS3(key: string): Promise<string> {
        try {
            const { GetObjectCommand } = await import('@aws-sdk/client-s3');
            const client = await this.createS3Client();

            const command = new GetObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: key
            });

            const result = await client.send(command);

            // 读取流数据 - 参考tools/s3-client.js的实现
            if (result.Body) {
                const chunks = [];
                for await (const chunk of result.Body as any) {
                    chunks.push(chunk);
                }
                return Buffer.concat(chunks).toString('utf-8');
            }

            throw new Error('响应体为空');
        } catch (error) {
            throw new Error(`S3下载失败: ${error.message}`);
        }
    }

    // S3上传文件
    private static async uploadToS3(key: string, data: string): Promise<void> {
        try {
            const { PutObjectCommand } = await import('@aws-sdk/client-s3');
            const client = await this.createS3Client();

            const command = new PutObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: key,
                Body: data,
                ContentType: 'application/json'
            });

            await client.send(command);
        } catch (error) {
            throw new Error(`S3上传失败: ${error.message}`);
        }
    }

    // 解密数据包
    private static async decryptData(encryptedData: string): Promise<any> {
        try {
            const data = JSON.parse(encryptedData);
            // 简化解密逻辑，实际需要使用crypto库
            const decrypted = atob(data.data);
            return JSON.parse(decrypted);
        } catch (error) {
            throw new Error('解密失败');
        }
    }

    // 加密数据包
    private static async encryptData(data: any): Promise<string> {
        try {
            // 简化加密逻辑，实际需要使用crypto库
            const encrypted = btoa(JSON.stringify(data));
            return JSON.stringify({ data: encrypted, algorithm: 'base64' });
        } catch (error) {
            throw new Error('加密失败');
        }
    }

    // 验证激活码 - S3集成版
    static async validateLicense(code: string): Promise<{ success: boolean; data?: LicenseInfo; error?: string }> {
        if (!code) return { success: false, error: '激活码不能为空' };

        try {
            // 下载激活码数据包
            const encryptedData = await this.downloadFromS3(`${code}.json`);

            // 解密数据包
            const licenseData = await this.decryptData(encryptedData);

            // 获取用户信息
            const user = await this.getSiYuanUserInfo();
            if (!user) return { success: false, error: '请先登录思源账号' };

            // 更新用户信息并重新上传
            licenseData.userId = user.userId;
            licenseData.userName = user.userName;

            const updatedData = await this.encryptData(licenseData);
            await this.uploadToS3(`${code}_used.json`, updatedData);

            // 构建许可证信息
            const licenseInfo: LicenseInfo = {
                code,
                userId: user.userId,
                userName: user.userName,
                activatedAt: Date.now(),
                expiresAt: licenseData.licenseType === 'dragon' ? 0 : Date.now() + (365 * 24 * 60 * 60 * 1000),
                type: licenseData.licenseType,
                isValid: true,
                features: this.getFeaturesForType(licenseData.licenseType)
            };

            return { success: true, data: licenseInfo };
        } catch (error) {
            return { success: false, error: '激活码验证失败' };
        }
    }

    static createTrialLicense(user: { userId: string; userName: string }): LicenseInfo {
        const now = Date.now();
        return {
            code: `TRIAL_${user.userId}_${now}`,
            userId: user.userId,
            userName: user.userName,
            activatedAt: now,
            expiresAt: now + this.TRIAL_DURATION,
            type: 'trial',
            isValid: true,
            features: this.getFeaturesForType('trial')
        };
    }

    // 检查许可证是否过期
    static isLicenseExpired(license: LicenseInfo): boolean {
        if (license.type === 'dragon') return false;
        return license.expiresAt > 0 && license.expiresAt < Date.now();
    }

    // 获取许可证剩余天数
    static getRemainingDays(license: LicenseInfo): number {
        if (license.type === 'dragon') return -1;
        const remaining = license.expiresAt - Date.now();
        return Math.max(0, Math.ceil(remaining / (24 * 60 * 60 * 1000)));
    }

    // 获取思源用户信息
    static async getSiYuanUserInfo(): Promise<{ userId: string; userName: string } | null> {
        try {
            // 优先使用全局对象
            if (typeof window !== 'undefined' && (window as any).siyuan?.user?.userId) {
                const user = (window as any).siyuan.user;
                return {
                    userId: user.userId,
                    userName: user.userName || user.userNickname || '思源用户'
                };
            }

            // 回退到API方式
            const response = await fetch('/api/system/getConf', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: '{}'
            });

            const data = await response.json();
            const user = data?.data?.conf?.user;

            return user?.userId ? {
                userId: user.userId,
                userName: user.userName || user.userNickname || '思源用户'
            } : null;
        } catch {
            return null;
        }
    }

    // 检查许可证是否有效
    static isLicenseValid(license: LicenseInfo | null): boolean {
        return !!(license && license.isValid && !this.isLicenseExpired(license));
    }

    // 获取当前许可证信息
    static async getCurrentLicense(licenseCode: string | null): Promise<LicenseInfo | null> {
        if (!licenseCode) return null;
        const result = await this.validateLicense(licenseCode);
        return result.success ? result.data || null : null;
    }

    // 获取会员类型信息
    static getMemberTypeInfo(type: string): { icon: string; name: string; color: string } {
        const typeMap = {
            dragon: { icon: '#iconDragon', name: '恶龙会员', color: '#ff6b35' },
            annual: { icon: '#iconVIP', name: '年度会员', color: '#4facfe' },
            trial: { icon: '#iconClock', name: '体验会员', color: '#95de64' }
        };
        return typeMap[type] || typeMap.trial;
    }

    // 处理Pro状态变更
    static async handleProState(
        enabled: boolean,
        licenseCode: string,
        currentLicenseCode: string | null
    ): Promise<{ success: boolean; message?: string; error?: string; license?: LicenseInfo; newLicenseCode?: string }> {
        if (!enabled) {
            return { success: true, message: 'Pro功能已停用', license: null };
        }

        if (licenseCode) {
            // 使用激活码激活
            const result = await this.validateLicense(licenseCode);
            return result.success && result.data ? {
                success: true,
                message: '激活成功！',
                license: result.data,
                newLicenseCode: licenseCode
            } : {
                success: false,
                error: result.error || '激活码验证失败'
            };
        }

        // 创建7天体验账号
        const user = await this.getSiYuanUserInfo();
        if (!user) {
            return { success: false, error: '请先登录思源账号' };
        }

        // 检查现有体验许可证
        if (currentLicenseCode) {
            const existingLicense = await this.getCurrentLicense(currentLicenseCode);
            if (existingLicense && existingLicense.type === 'trial' && !this.isLicenseExpired(existingLicense)) {
                return { success: true, message: '体验账号仍然有效', license: existingLicense };
            }
        }

        // 创建新体验许可证
        const trialLicense = this.createTrialLicense(user);
        return {
            success: true,
            message: '7天体验账号已开启！',
            license: trialLicense,
            newLicenseCode: trialLicense.code
        };
    }
}
