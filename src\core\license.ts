/**
 * 许可证管理器 - 极简版
 */

export interface LicenseInfo {
    code: string;
    userId: string;
    userName: string;
    activatedAt: number;
    expiresAt: number;
    type: 'dragon' | 'annual' | 'trial';
    isValid: boolean;
    features: string[];
}

export class LicenseManager {
    private static readonly TRIAL_DURATION = 7 * 24 * 60 * 60 * 1000; // 7天体验期

    private static getFeaturesForType(type: string): string[] {
        const features = {
            dragon: ['unlimited_cloud', 'batch_import', 'priority_support', 'advanced_features'],
            annual: ['cloud_sync', 'batch_import', 'standard_support'],
            trial: ['basic_features']
        };
        return features[type] || features.trial;
    }

    // 验证激活码 - 预留S3验证接口
    static async validateLicense(code: string): Promise<{ success: boolean; data?: LicenseInfo; error?: string }> {
        if (!code) return { success: false, error: '激活码不能为空' };

        // TODO: 集成S3验证
        // 暂时返回失败，等待S3验证实现
        return { success: false, error: '激活码验证功能开发中' };
    }

    static createTrialLicense(user: { userId: string; userName: string }): LicenseInfo {
        const now = Date.now();
        return {
            code: `TRIAL_${user.userId}_${now}`,
            userId: user.userId,
            userName: user.userName,
            activatedAt: now,
            expiresAt: now + this.TRIAL_DURATION,
            type: 'trial',
            isValid: true,
            features: this.getFeaturesForType('trial')
        };
    }

    // 检查许可证是否过期
    static isLicenseExpired(license: LicenseInfo): boolean {
        if (license.type === 'dragon') return false;
        return license.expiresAt > 0 && license.expiresAt < Date.now();
    }

    // 获取许可证剩余天数
    static getRemainingDays(license: LicenseInfo): number {
        if (license.type === 'dragon') return -1;
        const remaining = license.expiresAt - Date.now();
        return Math.max(0, Math.ceil(remaining / (24 * 60 * 60 * 1000)));
    }

    // 获取思源用户信息
    static async getSiYuanUserInfo(): Promise<{ userId: string; userName: string } | null> {
        try {
            // 优先使用全局对象
            if (typeof window !== 'undefined' && (window as any).siyuan?.user?.userId) {
                const user = (window as any).siyuan.user;
                return {
                    userId: user.userId,
                    userName: user.userName || user.userNickname || '思源用户'
                };
            }

            // 回退到API方式
            const response = await fetch('/api/system/getConf', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: '{}'
            });

            const data = await response.json();
            const user = data?.data?.conf?.user;

            return user?.userId ? {
                userId: user.userId,
                userName: user.userName || user.userNickname || '思源用户'
            } : null;
        } catch {
            return null;
        }
    }

    // 检查许可证是否有效
    static isLicenseValid(license: LicenseInfo | null): boolean {
        return !!(license && license.isValid && !this.isLicenseExpired(license));
    }

    // 获取当前许可证信息
    static async getCurrentLicense(licenseCode: string | null): Promise<LicenseInfo | null> {
        if (!licenseCode) return null;
        const result = await this.validateLicense(licenseCode);
        return result.success ? result.data || null : null;
    }

    // 获取会员类型信息
    static getMemberTypeInfo(type: string): { icon: string; name: string; color: string } {
        const typeMap = {
            dragon: { icon: '#iconDragon', name: '恶龙会员', color: '#ff6b35' },
            annual: { icon: '#iconVIP', name: '年度会员', color: '#4facfe' },
            trial: { icon: '#iconClock', name: '体验会员', color: '#95de64' }
        };
        return typeMap[type] || typeMap.trial;
    }

    // 处理Pro状态变更
    static async handleProState(
        enabled: boolean,
        licenseCode: string,
        currentLicenseCode: string | null
    ): Promise<{ success: boolean; message?: string; error?: string; license?: LicenseInfo; newLicenseCode?: string }> {
        if (!enabled) {
            return { success: true, message: 'Pro功能已停用', license: null };
        }

        if (licenseCode) {
            // 使用激活码激活
            const result = await this.validateLicense(licenseCode);
            return result.success && result.data ? {
                success: true,
                message: '激活成功！',
                license: result.data,
                newLicenseCode: licenseCode
            } : {
                success: false,
                error: result.error || '激活码验证失败'
            };
        }

        // 创建7天体验账号
        const user = await this.getSiYuanUserInfo();
        if (!user) {
            return { success: false, error: '请先登录思源账号' };
        }

        // 检查现有体验许可证
        if (currentLicenseCode) {
            const existingLicense = await this.getCurrentLicense(currentLicenseCode);
            if (existingLicense && existingLicense.type === 'trial' && !this.isLicenseExpired(existingLicense)) {
                return { success: true, message: '体验账号仍然有效', license: existingLicense };
            }
        }

        // 创建新体验许可证
        const trialLicense = this.createTrialLicense(user);
        return {
            success: true,
            message: '7天体验账号已开启！',
            license: trialLicense,
            newLicenseCode: trialLicense.code
        };
    }
}
