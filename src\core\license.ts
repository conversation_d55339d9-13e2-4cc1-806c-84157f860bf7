/**
 * 极简许可证管理器 - 优雅完美版
 */

import { S3Client, GetObjectCommand, PutObjectCommand, DeleteObjectCommand, CopyObjectCommand } from '@aws-sdk/client-s3';

export interface LicenseInfo {
    code?: string;
    userId: string;
    userName: string;
    activatedAt: number;
    expiresAt: number;
    type: 'dragon' | 'annual' | 'trial';
    isValid: boolean;
    features: string[];
}

// 极简许可证结构
interface License {
    type: 'trial' | 'annual' | 'dragon';
    userId: string;
    userName: string;
    activatedAt: number;
    expiresAt: number;      // 0 = 永久
    code?: string;          // 激活码（体验会员无）
    s3Path?: string;        // S3路径（体验会员无）
    lastCheck: number;      // 最后检查时间
    signature: string;      // 防篡改签名
}

export class LicenseManager {
    private static readonly LICENSE_FILE = 'license';
    private static readonly CHECK_INTERVAL = 24 * 60 * 60 * 1000; // 24小时

    // S3配置
    private static readonly S3_CONFIG = {
        accessKey: 'kFCOCF3QVYUcXHk75A5SC9VLPkYzVZZHPq2oApnk',
        secretKey: 'w5YsfJLlrJ3FJdafTq35ht5JYcc38svuDS3YI2Xp',
        bucket: 'siyuan-mediaplayer',
        region: 'cn-south-1',
        endpoint: 'https://s3.cn-south-1.qiniucs.com',
        encryptionKey: 'SiYuan_Fixed_Master_Key_2024_Secure_Change_This'
    };

    // 🎯 主入口 - 极简版
    static async toggle(code: string = '', plugin: any): Promise<{
        success: boolean;
        license?: LicenseInfo;
        message?: string;
        error?: string;
    }> {
        try {
            // 1. 加载现有许可证
            const existing = await this.load(plugin);

            // 2. 验证现有许可证
            if (existing && await this.validate(existing)) {
                return {
                    success: true,
                    license: this.toLicenseInfo(existing),
                    message: '许可证有效'
                };
            }

            // 3. 清除无效许可证
            if (existing) await this.clear(plugin);

            // 4. 创建新许可证
            const newLicense = await this.create(code.trim(), plugin);

            // 5. 保存并返回
            await this.save(newLicense, plugin);
            return {
                success: true,
                license: this.toLicenseInfo(newLicense),
                message: newLicense.type === 'trial' ? '体验会员激活成功' : '激活成功'
            };

        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // 📖 加载许可证
    private static async load(plugin: any): Promise<License | null> {
        try {
            const encrypted = await plugin.loadData(this.LICENSE_FILE);
            if (!encrypted) return null;

            const decrypted = await this.decrypt(encrypted);
            const license: License = JSON.parse(decrypted);

            // 验证签名
            if (!await this.verifySignature(license)) {
                console.warn('许可证签名无效');
                return null;
            }

            return license;
        } catch (error) {
            console.warn('加载许可证失败:', error.message);
            return null;
        }
    }

    // ✅ 验证许可证
    private static async validate(license: License): Promise<boolean> {
        try {
            // 1. 检查过期
            if (license.expiresAt > 0 && license.expiresAt < Date.now()) {
                return false;
            }

            // 2. 检查用户（仅正式许可证）
            if (license.type !== 'trial') {
                const user = await this.getSiYuanUserInfo();
                if (!user || user.userId !== license.userId) {
                    return false;
                }
            }

            // 3. 检查S3（仅正式许可证且需要时）
            if (license.type !== 'trial' && this.needsS3Check(license)) {
                const s3Valid = await this.checkS3(license.s3Path!);
                if (!s3Valid) return false;

                // 更新检查时间
                license.lastCheck = Date.now();
                license.signature = await this.generateSignature(license);
            }

            return true;
        } catch (error) {
            console.warn('验证失败:', error.message);
            return false;
        }
    }

    // 🆕 创建许可证
    private static async create(code: string, plugin: any): Promise<License> {
        const user = await this.getSiYuanUserInfo();

        if (!code) {
            // 创建体验会员
            return {
                type: 'trial',
                userId: user?.userId || 'guest',
                userName: user?.userName || '游客',
                activatedAt: Date.now(),
                expiresAt: Date.now() + (7 * 24 * 60 * 60 * 1000),
                lastCheck: Date.now(),
                signature: ''
            };
        }

        if (!user) throw new Error('请先登录思源账号');

        // 激活码流程
        const cleanUserName = user.userName.replace(/[^\w\u4e00-\u9fa5]/g, '');
        const userPath = `used/${code}_${user.userId}_${cleanUserName}`;

        // 检查用户专属文件
        if (await this.checkS3(userPath)) {
            const data = await this.downloadS3(userPath);
            return {
                type: data.licenseType,
                userId: user.userId,
                userName: user.userName,
                activatedAt: data.activatedAt,
                expiresAt: data.expiresAt,
                code: code,
                s3Path: userPath,
                lastCheck: Date.now(),
                signature: ''
            };
        }

        // 检查原始激活码
        const originalPath = `codes/${code}`;
        if (!await this.checkS3(originalPath)) {
            throw new Error('激活码不存在或已被使用');
        }

        const originalData = await this.downloadS3(originalPath);
        const activatedAt = Date.now();
        const expiresAt = originalData.licenseType === 'dragon' ? 0 :
                         activatedAt + (365 * 24 * 60 * 60 * 1000);

        // 重命名S3文件
        await this.renameS3(originalPath, userPath, {
            licenseType: originalData.licenseType,
            activatedAt,
            expiresAt,
            features: this.getFeatures(originalData.licenseType)
        });

        return {
            type: originalData.licenseType,
            userId: user.userId,
            userName: user.userName,
            activatedAt,
            expiresAt,
            code: code,
            s3Path: userPath,
            lastCheck: Date.now(),
            signature: ''
        };
    }

    // 💾 保存许可证
    private static async save(license: License, plugin: any): Promise<void> {
        license.signature = await this.generateSignature(license);
        const encrypted = await this.encrypt(JSON.stringify(license));
        await plugin.saveData(this.LICENSE_FILE, encrypted, 2);
    }

    // 🗑️ 清除许可证
    private static async clear(plugin: any): Promise<void> {
        await plugin.saveData(this.LICENSE_FILE, null, 2);
    }

    // 🔄 转换为LicenseInfo
    private static toLicenseInfo(license: License): LicenseInfo {
        return {
            code: license.code,
            userId: license.userId,
            userName: license.userName,
            activatedAt: license.activatedAt,
            expiresAt: license.expiresAt,
            type: license.type,
            isValid: true,
            features: this.getFeatures(license.type)
        };
    }

    // 🔐 加密解密（极简版）
    private static async encrypt(data: string): Promise<string> {
        const key = await crypto.subtle.digest('SHA-256', new TextEncoder().encode('siyuan-media-player'));
        const iv = crypto.getRandomValues(new Uint8Array(16));
        const keyObj = await crypto.subtle.importKey('raw', key, 'AES-GCM', false, ['encrypt']);
        const encrypted = await crypto.subtle.encrypt({ name: 'AES-GCM', iv }, keyObj, new TextEncoder().encode(data));

        const combined = new Uint8Array(iv.length + encrypted.byteLength);
        combined.set(iv);
        combined.set(new Uint8Array(encrypted), iv.length);
        return btoa(String.fromCharCode(...combined));
    }

    private static async decrypt(encryptedData: string): Promise<string> {
        const combined = Uint8Array.from(atob(encryptedData), c => c.charCodeAt(0));
        const iv = combined.slice(0, 16);
        const encrypted = combined.slice(16);

        const key = await crypto.subtle.digest('SHA-256', new TextEncoder().encode('siyuan-media-player'));
        const keyObj = await crypto.subtle.importKey('raw', key, 'AES-GCM', false, ['decrypt']);
        const decrypted = await crypto.subtle.decrypt({ name: 'AES-GCM', iv }, keyObj, encrypted);
        return new TextDecoder().decode(decrypted);
    }

    // 🔍 工具函数
    private static needsS3Check(license: License): boolean {
        return Date.now() - license.lastCheck > this.CHECK_INTERVAL;
    }

    private static async generateSignature(license: License): Promise<string> {
        const { signature, ...data } = license;
        const hash = await crypto.subtle.digest('SHA-256', new TextEncoder().encode(JSON.stringify(data) + 'salt'));
        return btoa(String.fromCharCode(...new Uint8Array(hash))).slice(0, 32);
    }

    private static async verifySignature(license: License): Promise<boolean> {
        const expected = await this.generateSignature(license);
        return expected === license.signature;
    }

    private static getFeatures(type: string): string[] {
        const features = {
            trial: ['basic_playback'],
            annual: ['cloud_sync', 'batch_import', 'standard_support'],
            dragon: ['cloud_sync', 'batch_import', 'priority_support', 'advanced_features']
        };
        return features[type] || features.trial;
    }

    // S3操作
    private static createS3Client(): S3Client {
        return new S3Client({
            region: this.S3_CONFIG.region,
            endpoint: this.S3_CONFIG.endpoint,
            credentials: {
                accessKeyId: this.S3_CONFIG.accessKey,
                secretAccessKey: this.S3_CONFIG.secretKey
            }
        });
    }

    private static async checkS3(path: string): Promise<boolean> {
        try {
            const client = this.createS3Client();
            const command = new GetObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: path
            });
            await client.send(command);
            return true;
        } catch (error) {
            return false;
        }
    }

    private static async downloadS3(path: string): Promise<any> {
        try {
            const client = this.createS3Client();
            const command = new GetObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: path
            });
            const response = await client.send(command);
            const data = await response.Body?.transformToString();
            return await this.decryptS3Data(data!);
        } catch (error) {
            throw new Error(`下载S3文件失败: ${error.message}`);
        }
    }

    private static async renameS3(oldPath: string, newPath: string, data: any): Promise<void> {
        try {
            const client = this.createS3Client();
            const encryptedData = await this.encryptS3Data(data);

            // 上传新文件
            const putCommand = new PutObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: newPath,
                Body: encryptedData,
                ContentType: 'application/json'
            });
            await client.send(putCommand);

            // 删除原文件
            const deleteCommand = new DeleteObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: oldPath
            });
            await client.send(deleteCommand);
        } catch (error) {
            throw new Error(`重命名S3文件失败: ${error.message}`);
        }
    }

    private static async encryptS3Data(data: any): Promise<string> {
        const jsonData = JSON.stringify(data);
        const salt = crypto.getRandomValues(new Uint8Array(16));
        const iv = crypto.getRandomValues(new Uint8Array(16));

        const keyMaterial = await crypto.subtle.importKey(
            'raw',
            new TextEncoder().encode(this.S3_CONFIG.encryptionKey),
            'PBKDF2',
            false,
            ['deriveKey']
        );

        const key = await crypto.subtle.deriveKey(
            {
                name: 'PBKDF2',
                salt: salt,
                iterations: 10000,
                hash: 'SHA-256'
            },
            keyMaterial,
            { name: 'AES-CBC', length: 256 },
            false,
            ['encrypt']
        );

        const encrypted = await crypto.subtle.encrypt(
            { name: 'AES-CBC', iv: iv },
            key,
            new TextEncoder().encode(jsonData)
        );

        return JSON.stringify({
            data: btoa(String.fromCharCode(...new Uint8Array(encrypted))),
            salt: btoa(String.fromCharCode(...salt)),
            iv: btoa(String.fromCharCode(...iv))
        });
    }

    private static async decryptS3Data(encryptedDataStr: string): Promise<any> {
        try {
            const encryptedData = JSON.parse(encryptedDataStr);
            const salt = Uint8Array.from(atob(encryptedData.salt), c => c.charCodeAt(0));
            const iv = Uint8Array.from(atob(encryptedData.iv), c => c.charCodeAt(0));

            const keyMaterial = await crypto.subtle.importKey(
                'raw',
                new TextEncoder().encode(this.S3_CONFIG.encryptionKey),
                'PBKDF2',
                false,
                ['deriveKey']
            );

            const key = await crypto.subtle.deriveKey(
                {
                    name: 'PBKDF2',
                    salt: salt,
                    iterations: 10000,
                    hash: 'SHA-256'
                },
                keyMaterial,
                { name: 'AES-CBC', length: 256 },
                false,
                ['decrypt']
            );

            const encryptedBytes = Uint8Array.from(atob(encryptedData.data), c => c.charCodeAt(0));
            const decrypted = await crypto.subtle.decrypt(
                { name: 'AES-CBC', iv: iv },
                key,
                encryptedBytes
            );

            // 处理gzip解压缩
            try {
                const decompressedStream = new DecompressionStream('gzip');
                const writer = decompressedStream.writable.getWriter();
                const reader = decompressedStream.readable.getReader();

                writer.write(new Uint8Array(decrypted));
                writer.close();

                const chunks = [];
                let done = false;
                while (!done) {
                    const { value, done: readerDone } = await reader.read();
                    done = readerDone;
                    if (value) chunks.push(value);
                }

                const decompressed = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));
                let offset = 0;
                for (const chunk of chunks) {
                    decompressed.set(chunk, offset);
                    offset += chunk.length;
                }

                const decryptedText = new TextDecoder().decode(decompressed);
                return JSON.parse(decryptedText);
            } catch (gzipError) {
                const decryptedText = new TextDecoder().decode(decrypted);
                return JSON.parse(decryptedText);
            }
        } catch (error) {
            throw new Error(`S3数据解密失败: ${error.message}`);
        }
    }

    private static async getSiYuanUserInfo(): Promise<{ userId: string; userName: string } | null> {
        try {
            const response = await fetch('/api/system/getConf');
            const data = await response.json();
            if (data.code === 0 && data.data.user) {
                return {
                    userId: data.data.user.userId,
                    userName: data.data.user.userName
                };
            }
            return null;
        } catch (error) {
            console.warn('获取用户信息失败:', error);
            return null;
        }
    }

    // 兼容性方法 - 保持与Setting.svelte的接口一致
    static async handleProState(
        enabled: boolean,
        licenseCode: string,
        currentLicenseCode: string | null,
        plugin?: any
    ): Promise<{ success: boolean; message?: string; error?: string; license?: LicenseInfo; newLicenseCode?: string }> {
        if (!enabled) {
            await this.clear(plugin);
            return { success: true, message: 'Pro功能已关闭' };
        }

        const result = await this.toggle(licenseCode, plugin);
        return {
            success: result.success,
            message: result.message,
            error: result.error,
            license: result.license,
            newLicenseCode: result.license?.code || licenseCode
        };
    }

    static async getCurrentLicense(licenseCode: string | null, plugin?: any): Promise<LicenseInfo | null> {
        const existing = await this.load(plugin);
        if (existing && await this.validate(existing)) {
            return this.toLicenseInfo(existing);
        }
        return null;
    }
}
        return new S3Client({
            region: this.S3_CONFIG.region,
            endpoint: this.S3_CONFIG.endpoint,
            credentials: {
                accessKeyId: this.S3_CONFIG.accessKey,
                secretAccessKey: this.S3_CONFIG.secretKey
            },
            forcePathStyle: true // 七牛云需要使用path-style
        });
    }

    // S3下载文件
    private static async downloadFromS3(key: string): Promise<string> {
        try {
            const client = this.createS3Client();

            const command = new GetObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: key
            });

            const result = await client.send(command);

            // 读取流数据 - 参考tools/s3-client.js的实现
            if (result.Body) {
                const chunks = [];
                for await (const chunk of result.Body as any) {
                    chunks.push(chunk);
                }
                return Buffer.concat(chunks).toString('utf-8');
            }

            throw new Error('响应体为空');
        } catch (error) {
            throw new Error(`S3下载失败: ${error.message}`);
        }
    }

    // S3重命名文件（通过复制+删除实现）
    private static async renameS3File(oldKey: string, newKey: string): Promise<void> {
        try {
            const client = this.createS3Client();

            // 1. 复制文件到新位置
            const copyCommand = new CopyObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                CopySource: `${this.S3_CONFIG.bucket}/${oldKey}`,
                Key: newKey
            });

            await client.send(copyCommand);

            // 2. 删除原文件
            const deleteCommand = new DeleteObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: oldKey
            });

            await client.send(deleteCommand);
        } catch (error) {
            throw new Error(`S3重命名失败: ${error.message}`);
        }
    }

    // 解密数据包 - 简化版本，处理gzip压缩
    private static async decryptData(encryptedDataStr: string): Promise<any> {
        try {
            const encryptedData = JSON.parse(encryptedDataStr);

            // 使用Web Crypto API解密
            const salt = Uint8Array.from(atob(encryptedData.salt), c => c.charCodeAt(0));
            const iv = Uint8Array.from(atob(encryptedData.iv), c => c.charCodeAt(0));

            // 生成密钥
            const keyMaterial = await crypto.subtle.importKey(
                'raw',
                new TextEncoder().encode(this.S3_CONFIG.encryptionKey),
                'PBKDF2',
                false,
                ['deriveKey']
            );

            const key = await crypto.subtle.deriveKey(
                {
                    name: 'PBKDF2',
                    salt: salt,
                    iterations: 10000,
                    hash: 'SHA-256'
                },
                keyMaterial,
                { name: 'AES-CBC', length: 256 },
                false,
                ['decrypt']
            );

            // 解密数据
            const encryptedBytes = Uint8Array.from(atob(encryptedData.data), c => c.charCodeAt(0));
            const decrypted = await crypto.subtle.decrypt(
                { name: 'AES-CBC', iv: iv },
                key,
                encryptedBytes
            );

            // 处理gzip解压缩 - 使用DecompressionStream API
            try {
                const decompressedStream = new DecompressionStream('gzip');
                const writer = decompressedStream.writable.getWriter();
                const reader = decompressedStream.readable.getReader();

                writer.write(new Uint8Array(decrypted));
                writer.close();

                const chunks = [];
                let done = false;
                while (!done) {
                    const { value, done: readerDone } = await reader.read();
                    done = readerDone;
                    if (value) chunks.push(value);
                }

                const decompressed = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));
                let offset = 0;
                for (const chunk of chunks) {
                    decompressed.set(chunk, offset);
                    offset += chunk.length;
                }

                const decryptedText = new TextDecoder().decode(decompressed);
                return JSON.parse(decryptedText);
            } catch (gzipError) {
                // 如果gzip解压失败，尝试直接解析（可能没有压缩）
                const decryptedText = new TextDecoder().decode(decrypted);
                return JSON.parse(decryptedText);
            }
        } catch (error) {
            throw new Error(`解密失败: ${error.message}`);
        }
    }



    // 检查S3文件是否存在
    private static async checkS3FileExists(key: string): Promise<boolean> {
        try {
            const client = this.createS3Client();
            const command = new GetObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: key
            });
            await client.send(command);
            return true;
        } catch (error) {
            return false;
        }
    }

    // 生成用户专属激活码（不含扩展名）
    private static generateUserLicenseCode(code: string, user: { userId: string; userName: string }): string {
        const cleanUserName = user.userName.replace(/[^a-zA-Z0-9]/g, '');
        return `${code}_${user.userId}_${cleanUserName}`;
    }

    // 生成用户专属激活码文件名（含扩展名）
    private static generateUserLicenseKey(code: string, user: { userId: string; userName: string }): string {
        return `${this.generateUserLicenseCode(code, user)}.dat`;
    }

    // 生成许可证签名（防篡改）
    private static generateLicenseSignature(license: Omit<LocalLicense, 'signature'>): string {
        const data = JSON.stringify(license);
        const key = this.S3_CONFIG.encryptionKey;
        // 简化签名：使用固定密钥 + 数据的哈希
        return btoa(data + key).slice(0, 32);
    }

    // 验证许可证签名
    private static verifyLicenseSignature(license: LocalLicense): boolean {
        const { signature, ...licenseData } = license;
        const expectedSignature = this.generateLicenseSignature(licenseData);
        return signature === expectedSignature;
    }

    // 生成标准AES密钥（256位）
    private static async generateAESKey(): Promise<Uint8Array> {
        const keyString = this.S3_CONFIG.encryptionKey;
        const keyBytes = new TextEncoder().encode(keyString);

        // 使用SHA-256哈希生成固定长度的密钥
        const hashBuffer = await crypto.subtle.digest('SHA-256', keyBytes);
        return new Uint8Array(hashBuffer); // 32字节 = 256位
    }

    // 加密本地许可证数据
    private static async encryptLocalLicense(license: LocalLicense): Promise<string> {
        try {
            const data = JSON.stringify(license);
            const key = await this.generateAESKey();
            const iv = crypto.getRandomValues(new Uint8Array(16));

            // 使用AES-GCM加密
            const keyMaterial = await crypto.subtle.importKey('raw', key, 'AES-GCM', false, ['encrypt']);
            const encrypted = await crypto.subtle.encrypt({ name: 'AES-GCM', iv }, keyMaterial, new TextEncoder().encode(data));

            // 组合IV和加密数据
            const combined = new Uint8Array(iv.length + encrypted.byteLength);
            combined.set(iv);
            combined.set(new Uint8Array(encrypted), iv.length);

            return btoa(String.fromCharCode(...combined));
        } catch (error) {
            throw new Error(`加密失败: ${error.message}`);
        }
    }

    // 验证Base64字符串
    private static isValidBase64(str: string): boolean {
        try {
            // 检查是否为有效的Base64格式
            const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
            if (!base64Regex.test(str)) return false;

            // 尝试解码
            atob(str);
            return true;
        } catch {
            return false;
        }
    }

    // 解密本地许可证数据
    private static async decryptLocalLicense(encryptedData: string): Promise<LocalLicense> {
        try {
            // 验证输入数据
            if (!encryptedData || typeof encryptedData !== 'string') {
                throw new Error('无效的加密数据格式');
            }

            // 验证Base64格式
            if (!this.isValidBase64(encryptedData)) {
                throw new Error('数据不是有效的Base64格式');
            }

            const combined = Uint8Array.from(atob(encryptedData), c => c.charCodeAt(0));

            // 验证数据长度
            if (combined.length < 16) {
                throw new Error('加密数据长度不足');
            }

            const iv = combined.slice(0, 16);
            const encrypted = combined.slice(16);

            const key = await this.generateAESKey();
            const keyMaterial = await crypto.subtle.importKey('raw', key, 'AES-GCM', false, ['decrypt']);
            const decrypted = await crypto.subtle.decrypt({ name: 'AES-GCM', iv }, keyMaterial, encrypted);

            const data = new TextDecoder().decode(decrypted);
            return JSON.parse(data);
        } catch (error) {
            throw new Error(`解密失败: ${error.message}`);
        }
    }

    // 保存本地许可证（加密存储，无明显后缀）
    private static async saveLocalLicense(license: LocalLicense, plugin: any): Promise<void> {
        try {
            const encryptedData = await this.encryptLocalLicense(license);
            // 使用不明显的文件名
            await plugin.saveData('config.dat', encryptedData, 2);
        } catch (error) {
            console.warn('保存本地许可证失败:', error);
        }
    }

    // 清理Base64字符串
    private static cleanBase64String(str: string): string {
        // 移除所有非Base64字符（保留A-Z, a-z, 0-9, +, /, =）
        return str.replace(/[^A-Za-z0-9+/=]/g, '');
    }

    // 读取本地许可证（解密验证）
    private static async loadLocalLicense(plugin: any): Promise<LocalLicense | null> {
        try {
            let encryptedData = await plugin.loadData('config.dat');

            // 检查数据是否存在
            if (!encryptedData) {
                console.log('本地许可证文件不存在');
                return null;
            }

            // 检查数据类型并处理
            if (typeof encryptedData !== 'string') {
                console.warn('本地许可证数据格式错误，期望字符串，实际:', typeof encryptedData);
                // 尝试转换为字符串
                if (encryptedData && typeof encryptedData === 'object') {
                    // 可能是JSON对象，尝试提取字符串值
                    encryptedData = JSON.stringify(encryptedData);
                } else {
                    await plugin.saveData('config.dat', null, 2);
                    return null;
                }
            }

            console.log('读取到加密数据长度:', encryptedData.length);
            console.log('原始数据前50字符:', encryptedData.substring(0, 50));

            // 清理Base64字符串
            const cleanedData = this.cleanBase64String(encryptedData);
            console.log('清理后数据长度:', cleanedData.length);
            console.log('清理后数据前50字符:', cleanedData.substring(0, 50));

            // 解密数据
            const license = await this.decryptLocalLicense(cleanedData);

            // 验证签名
            if (!this.verifyLicenseSignature(license)) {
                console.warn('本地许可证签名验证失败');
                await plugin.saveData('config.dat', null, 2); // 清除无效许可证
                return null;
            }

            console.log('本地许可证读取成功');
            return license;
        } catch (error) {
            console.warn('读取本地许可证失败:', error.message);
            // 清除损坏的数据
            try {
                await plugin.saveData('config.dat', null, 2);
                console.log('已清除损坏的许可证数据');
            } catch (cleanupError) {
                console.warn('清理损坏数据失败:', cleanupError);
            }
            return null;
        }
    }

    // 验证激活码 - 智能版（首次激活vs后续验证）
    static async validateLicense(code: string, plugin?: any): Promise<{ success: boolean; data?: LicenseInfo; error?: string; userLicenseCode?: string }> {
        if (!code) return { success: false, error: '激活码不能为空' };

        try {
            // 获取用户信息
            const user = await this.getSiYuanUserInfo();
            if (!user) return { success: false, error: '请先登录思源账号' };

            // 生成用户专属激活码和文件名
            const userLicenseCode = this.generateUserLicenseCode(code, user);
            const userLicenseKey = this.generateUserLicenseKey(code, user);

            // 检查是否已经是用户专属激活码（包含用户信息）
            const isUserSpecificCode = code.includes(`_${user.userId}_`);

            if (isUserSpecificCode) {
                // 情况1：已经是用户专属激活码，使用本地许可证
                if (!plugin) {
                    return { success: false, error: '插件实例未提供，无法读取本地许可证' };
                }

                const localLicense = await this.loadLocalLicense(plugin);

                if (!localLicense || localLicense.userLicenseCode !== code) {
                    return { success: false, error: '本地许可证不存在或不匹配' };
                }

                // 检查S3文件是否仍然存在（在线验证）
                const fileKey = `${code}.dat`;
                const exists = await this.checkS3FileExists(fileKey);
                if (!exists) {
                    return { success: false, error: '激活码已失效或不存在' };
                }

                // 使用本地许可证的准确信息
                const licenseInfo: LicenseInfo = {
                    code: localLicense.originalCode,
                    userId: localLicense.userId,
                    userName: localLicense.userName,
                    activatedAt: localLicense.activatedAt,
                    expiresAt: localLicense.expiresAt,
                    type: localLicense.licenseType,
                    isValid: true,
                    features: localLicense.features
                };

                return { success: true, data: licenseInfo, userLicenseCode: code };
            } else {
                // 情况2：首次激活，需要完整流程

                // 检查用户专属文件是否已存在（防止重复激活）
                const userFileExists = await this.checkS3FileExists(userLicenseKey);
                if (userFileExists) {
                    return { success: false, error: '该激活码已被当前用户使用' };
                }

                // 下载原始激活码数据包
                const encryptedData = await this.downloadFromS3(`${code}.dat`);

                // 解密数据包进行验证
                const licenseData = await this.decryptData(encryptedData);

                // 验证许可证有效性
                if (!licenseData || !licenseData.licenseType) {
                    return { success: false, error: '激活码数据无效' };
                }

                // 重命名文件标记已使用
                await this.renameS3File(`${code}.dat`, userLicenseKey);

                // 构建许可证信息
                const activatedAt = Date.now();
                const expiresAt = licenseData.licenseType === 'dragon' ? 0 : activatedAt + (365 * 24 * 60 * 60 * 1000);
                const features = this.getFeaturesForType(licenseData.licenseType);

                const licenseInfo: LicenseInfo = {
                    code,
                    userId: user.userId,
                    userName: user.userName,
                    activatedAt,
                    expiresAt,
                    type: licenseData.licenseType,
                    isValid: true,
                    features
                };

                // 创建并保存本地许可证
                const localLicense: LocalLicense = {
                    originalCode: code,
                    userLicenseCode: userLicenseCode,
                    userId: user.userId,
                    userName: user.userName,
                    licenseType: licenseData.licenseType,
                    activatedAt,
                    expiresAt,
                    features,
                    signature: '' // 临时占位
                };

                // 生成签名
                localLicense.signature = this.generateLicenseSignature(localLicense);

                // 保存到本地
                if (plugin) {
                    await this.saveLocalLicense(localLicense, plugin);
                }

                return { success: true, data: licenseInfo, userLicenseCode: userLicenseCode };
            }
        } catch (error) {
            return { success: false, error: `激活码验证失败: ${error.message}` };
        }
    }

    static createTrialLicense(user: { userId: string; userName: string }): LicenseInfo {
        const now = Date.now();
        return {
            code: `TRIAL_${user.userId}_${now}`,
            userId: user.userId,
            userName: user.userName,
            activatedAt: now,
            expiresAt: now + this.TRIAL_DURATION,
            type: 'trial',
            isValid: true,
            features: this.getFeaturesForType('trial')
        };
    }

    // 检查许可证是否过期
    static isLicenseExpired(license: LicenseInfo): boolean {
        if (license.type === 'dragon') return false;
        return license.expiresAt > 0 && license.expiresAt < Date.now();
    }

    // 获取许可证剩余天数
    static getRemainingDays(license: LicenseInfo): number {
        if (license.type === 'dragon') return -1;
        const remaining = license.expiresAt - Date.now();
        return Math.max(0, Math.ceil(remaining / (24 * 60 * 60 * 1000)));
    }

    // 获取思源用户信息
    static async getSiYuanUserInfo(): Promise<{ userId: string; userName: string } | null> {
        try {
            // 优先使用全局对象
            if (typeof window !== 'undefined' && (window as any).siyuan?.user?.userId) {
                const user = (window as any).siyuan.user;
                return {
                    userId: user.userId,
                    userName: user.userName || user.userNickname || '思源用户'
                };
            }

            // 回退到API方式
            const response = await fetch('/api/system/getConf', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: '{}'
            });

            const data = await response.json();
            const user = data?.data?.conf?.user;

            return user?.userId ? {
                userId: user.userId,
                userName: user.userName || user.userNickname || '思源用户'
            } : null;
        } catch {
            return null;
        }
    }

    // 检查许可证是否有效
    static isLicenseValid(license: LicenseInfo | null): boolean {
        return !!(license && license.isValid && !this.isLicenseExpired(license));
    }

    // 获取当前许可证信息
    static async getCurrentLicense(licenseCode: string | null, plugin?: any): Promise<LicenseInfo | null> {
        if (!licenseCode) return null;
        const result = await this.validateLicense(licenseCode, plugin);
        return result.success ? result.data || null : null;
    }

    // 获取会员类型信息
    static getMemberTypeInfo(type: string): { icon: string; name: string; color: string } {
        const typeMap = {
            dragon: { icon: '#iconDragon', name: '恶龙会员', color: '#ff6b35' },
            annual: { icon: '#iconVIP', name: '年度会员', color: '#4facfe' },
            trial: { icon: '#iconClock', name: '体验会员', color: '#95de64' }
        };
        return typeMap[type] || typeMap.trial;
    }

    // 处理Pro状态变更
    static async handleProState(
        enabled: boolean,
        licenseCode: string,
        currentLicenseCode: string | null,
        plugin?: any
    ): Promise<{ success: boolean; message?: string; error?: string; license?: LicenseInfo; newLicenseCode?: string }> {
        if (!enabled) {
            return { success: true, message: 'Pro功能已停用', license: null };
        }

        if (licenseCode) {
            // 使用激活码激活
            const result = await this.validateLicense(licenseCode, plugin);
            return result.success && result.data ? {
                success: true,
                message: '激活成功！',
                license: result.data,
                newLicenseCode: result.userLicenseCode || licenseCode // 保存用户专属激活码
            } : {
                success: false,
                error: result.error || '激活码验证失败'
            };
        }

        // 创建7天体验账号
        const user = await this.getSiYuanUserInfo();
        if (!user) {
            return { success: false, error: '请先登录思源账号' };
        }

        // 检查现有体验许可证
        if (currentLicenseCode) {
            const existingLicense = await this.getCurrentLicense(currentLicenseCode);
            if (existingLicense && existingLicense.type === 'trial' && !this.isLicenseExpired(existingLicense)) {
                return { success: true, message: '体验账号仍然有效', license: existingLicense };
            }
        }

        // 创建新体验许可证
        const trialLicense = this.createTrialLicense(user);
        return {
            success: true,
            message: '7天体验账号已开启！',
            license: trialLicense,
            newLicenseCode: trialLicense.code
        };
    }
}
