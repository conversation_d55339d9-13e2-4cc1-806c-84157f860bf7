/**
 * 许可证管理器 - 极简版
 */

export interface LicenseInfo {
    code: string;
    userId: string;
    userName: string;
    activatedAt: number;
    expiresAt: number;
    type: 'dragon' | 'annual' | 'trial';
    maxDevices: number;
    isValid: boolean;
    features: string[];
    restrictions?: { maxCloudFiles: number; maxBatchImport: number };
}

export class LicenseManager {
    // 字符集定义
    private static readonly CHARS = "ABCDEFGHJKMNPQRSTUVWXYZ23456789";

    // 本地体验会员配置
    private static readonly TRIAL_DURATION = 7 * 24 * 60 * 60 * 1000; // 7天体验期

    // 简化的激活码验证 - 只进行基本格式检查
    private static validateCodeFormat(code: string): boolean {
        return /^[A-Z0-9]{24}$/.test(code) && code.split('').every(char => this.CHARS.includes(char));
    }

    private static getTypeInfo(type: string): { devices: number; days: number } {
        const info = { dragon: { devices: 5, days: 0 }, annual: { devices: 5, days: 365 }, trial: { devices: 1, days: 7 } };
        return info[type] || { devices: 1, days: 7 };
    }

    private static getFeaturesForType(type: string): string[] {
        const features = {
            dragon: ['unlimited_cloud', 'batch_import', 'priority_support', 'advanced_features'],
            annual: ['cloud_sync', 'batch_import', 'standard_support'],
            trial: ['basic_features']
        };
        return features[type] || features.trial;
    }

    // 验证激活码 - 简化版本，支持体验账号
    static async validateLicense(code: string, actualUserId?: string): Promise<{ success: boolean; data?: LicenseInfo; error?: string }> {
        try {
            if (!code || code.length < 10) return { success: false, error: '激活码格式错误' };

            const cleanCode = code.replace(/[-\s]/g, '').toUpperCase();

            // 基本格式验证
            if (!this.validateCodeFormat(cleanCode)) {
                return { success: false, error: '激活码格式不正确' };
            }

            // 获取用户信息
            const user = actualUserId ? { userId: actualUserId, userName: '当前用户' } : await this.getSiYuanUserInfo();
            const userId = user?.userId || 'local_user';
            const userName = user?.userName || '本地用户';

            // 简化验证：所有格式正确的激活码都作为体验版处理
            const licenseInfo: LicenseInfo = {
                code: cleanCode,
                userId,
                userName,
                activatedAt: Date.now(),
                expiresAt: Date.now() + this.TRIAL_DURATION,
                type: 'trial',
                maxDevices: 1,
                isValid: true,
                features: this.getFeaturesForType('trial'),
                restrictions: { maxCloudFiles: 100, maxBatchImport: 20 }
            };

            console.log('✅ 激活码验证通过，创建体验版许可证');
            return { success: true, data: licenseInfo };

        } catch (error) {
            return { success: false, error: error.message || '激活码验证失败' };
        }
    }

    // 生成体验激活码（简化版）
    private static generateTrialCode(userId: string): string {
        const now = Date.now();
        const randomPart = Math.random().toString(36).substring(2, 15);
        const userPart = userId.substring(0, 4).padEnd(4, '0');
        const timePart = (now % 1000000).toString().padStart(6, '0');
        
        // 组合并填充到24位
        let code = (randomPart + userPart + timePart).toUpperCase().substring(0, 24);
        
        // 确保只包含有效字符
        code = code.split('').map(char => 
            this.CHARS.includes(char) ? char : this.CHARS[Math.floor(Math.random() * this.CHARS.length)]
        ).join('');
        
        return code.padEnd(24, this.CHARS[0]);
    }

    static createTrialLicense(user: { userId: string; userName: string }): LicenseInfo {
        const now = Date.now();
        return {
            code: this.generateTrialCode(user.userId),
            userId: user.userId,
            userName: user.userName,
            activatedAt: now,
            expiresAt: now + this.TRIAL_DURATION,
            type: 'trial',
            maxDevices: 1,
            isValid: true,
            features: this.getFeaturesForType('trial'),
            restrictions: { maxCloudFiles: 100, maxBatchImport: 20 }
        };
    }

    // 检查许可证是否过期
    static isLicenseExpired(license: LicenseInfo): boolean {
        if (license.type === 'dragon') return false; // 永久许可证
        return license.expiresAt > 0 && license.expiresAt < Date.now();
    }

    // 获取许可证剩余天数
    static getRemainingDays(license: LicenseInfo): number {
        if (license.type === 'dragon') return -1; // 永久
        if (license.expiresAt <= 0) return -1;
        
        const remaining = license.expiresAt - Date.now();
        return Math.max(0, Math.ceil(remaining / (24 * 60 * 60 * 1000)));
    }

    // 检查功能是否可用
    static hasFeature(license: LicenseInfo, feature: string): boolean {
        return license.isValid && !this.isLicenseExpired(license) && license.features.includes(feature);
    }

    // 获取许可证状态描述
    static getLicenseStatus(license: LicenseInfo): string {
        if (!license.isValid) return '无效';
        if (this.isLicenseExpired(license)) return '已过期';

        const remaining = this.getRemainingDays(license);
        if (remaining === -1) return '永久有效';
        if (remaining === 0) return '今天到期';
        if (remaining <= 7) return `${remaining}天后到期`;

        return '有效';
    }

    // 获取思源用户信息
    static async getSiYuanUserInfo(): Promise<{ userId: string; userName: string } | null> {
        try {
            // 方法1：优先使用全局对象（更可靠）
            if (typeof window !== 'undefined' && (window as any).siyuan?.user?.userId) {
                const user = (window as any).siyuan.user;
                return {
                    userId: user.userId,
                    userName: user.userName || user.userNickname || '思源用户'
                };
            }

            // 方法2：回退到API方式
            const response = await fetch('/api/system/getConf', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: '{}'
            });

            if (!response.ok) return null;

            const data = await response.json();
            const user = data?.data?.conf?.user;

            if (user?.userId) {
                return {
                    userId: user.userId,
                    userName: user.userName || user.userNickname || '思源用户'
                };
            }

            return null;
        } catch (error) {
            console.warn('获取思源用户信息失败:', error);
            return null;
        }
    }

    // 检查许可证是否有效（未过期且有效）
    static isLicenseValid(license: LicenseInfo | null): boolean {
        return !!(license && license.isValid && !this.isLicenseExpired(license));
    }

    // 获取当前许可证信息
    static async getCurrentLicense(licenseCode: string | null): Promise<LicenseInfo | null> {
        if (!licenseCode) return null;

        try {
            const result = await this.validateLicense(licenseCode);
            return result.success ? result.data || null : null;
        } catch (error) {
            console.warn('获取当前许可证失败:', error);
            return null;
        }
    }

    // 获取会员类型信息（用于UI显示）
    static getMemberTypeInfo(type: string): { icon: string; name: string; color: string } {
        const typeMap = {
            dragon: { icon: '#iconDragon', name: '恶龙会员', color: '#ff6b35' },
            annual: { icon: '#iconVIP', name: '年度会员', color: '#4facfe' },
            trial: { icon: '#iconClock', name: '体验会员', color: '#95de64' }
        };
        return typeMap[type] || typeMap.trial;
    }

    // 处理Pro状态变更（激活/停用）
    static async handleProState(
        enabled: boolean,
        licenseCode: string,
        currentLicenseCode: string | null
    ): Promise<{ success: boolean; message?: string; error?: string; license?: LicenseInfo; newLicenseCode?: string }> {
        try {
            if (enabled) {
                // 启用Pro - 需要激活码或创建体验账号
                if (licenseCode) {
                    // 使用激活码激活
                    const result = await this.validateLicense(licenseCode);
                    if (result.success && result.data) {
                        return {
                            success: true,
                            message: '激活成功！',
                            license: result.data,
                            newLicenseCode: licenseCode
                        };
                    } else {
                        return {
                            success: false,
                            error: result.error || '激活码验证失败'
                        };
                    }
                } else {
                    // 创建7天体验账号
                    const user = await this.getSiYuanUserInfo();
                    if (!user) {
                        return {
                            success: false,
                            error: '请先登录思源账号'
                        };
                    }

                    // 检查是否已经有体验许可证
                    if (currentLicenseCode) {
                        const existingLicense = await this.getCurrentLicense(currentLicenseCode);
                        if (existingLicense && existingLicense.type === 'trial' && !this.isLicenseExpired(existingLicense)) {
                            return {
                                success: true,
                                message: '体验账号仍然有效',
                                license: existingLicense
                            };
                        }
                    }

                    // 创建新的体验许可证
                    const trialLicense = this.createTrialLicense(user);
                    return {
                        success: true,
                        message: '7天体验账号已开启！',
                        license: trialLicense,
                        newLicenseCode: trialLicense.code
                    };
                }
            } else {
                // 停用Pro
                return {
                    success: true,
                    message: 'Pro功能已停用',
                    license: null
                };
            }
        } catch (error) {
            return {
                success: false,
                error: error.message || '操作失败'
            };
        }
    }
}
