/**
 * 🚀 极限精简许可证管理器 - 终极版
 * 极限精简，简洁高效，优雅完美
 */

export interface LicenseInfo {
    type: 'trial' | 'annual' | 'dragon';
    userId: string;
    userName: string;
    activatedAt: number;
    expiresAt: number;
    code?: string;
    isValid: boolean;
    features: string[];
    lastCheck: number;
}

export class LicenseManager {
    private static readonly LICENSE_FILE = 'license';
    private static readonly CHECK_INTERVAL = 24 * 60 * 60 * 1000;
    private static readonly S3_CONFIG = {
        accessKey: 'kFCOCF3QVYUcXHk75A5SC9VLPkYzVZZHPq2oApnk',
        secretKey: 'w5YsfJLlrJ3FJdafTq35ht5JYcc38svuDS3YI2Xp',
        bucket: 'siyuan-mediaplayer',
        region: 'cn-south-1',
        encryptionKey: 'SiYuan_Fixed_Master_Key_2024_Secure_Change_This'
    };

    // 🎯 唯一入口 - 极限精简
    static async activate(code = '', plugin: any) {
        try {
            if (code.trim()) {
                const license = await this.activateCode(code.trim(), plugin);
                await this.save(license, plugin);
                return { success: true, license, message: '激活成功' };
            }

            const existing = await this.load(plugin);
            if (existing && this.isValid(existing)) {
                return { success: true, license: existing, message: '许可证有效' };
            }

            if (existing) await this.clear(plugin);
            const license = await this.createTrial();
            await this.save(license, plugin);
            return { success: true, license, message: '体验会员激活成功' };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // 📖 加载许可证
    static async load(plugin: any): Promise<LicenseInfo | null> {
        try {
            const encrypted = await plugin.loadData(this.LICENSE_FILE);
            if (!encrypted) return null;
            const decrypted = await this.decrypt(encrypted);
            const license: LicenseInfo = JSON.parse(decrypted);
            return this.verifySignature(license) ? license : null;
        } catch (error) {
            await this.clear(plugin);
            return null;
        }
    }

    // ✅ 验证有效性
    private static isValid(license: LicenseInfo): boolean {
        if (license.expiresAt > 0 && license.expiresAt < Date.now()) return false;
        if (license.type !== 'trial' && Date.now() - license.lastCheck > this.CHECK_INTERVAL) return false;
        return true;
    }

    // 🆕 创建体验会员
    private static async createTrial(): Promise<LicenseInfo> {
        const user = await this.getSiYuanUserInfo();
        return {
            type: 'trial',
            userId: user?.userId || 'guest',
            userName: user?.userName || '游客',
            activatedAt: Date.now(),
            expiresAt: Date.now() + (7 * 24 * 60 * 60 * 1000),
            isValid: true,
            features: ['basic_playback'],
            lastCheck: Date.now()
        };
    }

    // 🔥 激活码激活 - 智能策略
    private static async activateCode(code: string, plugin: any): Promise<LicenseInfo> {
        const user = await this.getSiYuanUserInfo();
        if (!user) throw new Error('请先登录思源账号');

        const existing = await this.load(plugin);
        const cleanUserName = user.userName.replace(/[^\w\u4e00-\u9fa5]/g, '');
        const userPath = `${code}_${user.userId}_${cleanUserName}.dat`;

        // 智能选择策略：无许可证或体验许可证 → 直接查原始；正式许可证 → 先查用户专属
        if (!existing || existing.type === 'trial') {
            return await this.activateFromOriginal(code, user, cleanUserName);
        } else {
            try {
                const data = await this.downloadS3(userPath);
                return this.buildLicense(data, user, code);
            } catch (error) {
                return await this.activateFromOriginal(code, user, cleanUserName);
            }
        }
    }

    // 🔥 从原始激活码激活
    private static async activateFromOriginal(code: string, user: any, cleanUserName: string): Promise<LicenseInfo> {
        const originalData = await this.downloadS3(`${code}.dat`);
        const activatedAt = Date.now();
        const expiresAt = originalData.licenseType === 'dragon' ? 0 : activatedAt + (365 * 24 * 60 * 60 * 1000);

        const userPath = `${code}_${user.userId}_${cleanUserName}.dat`;
        await this.renameS3(`${code}.dat`, userPath, {
            licenseType: originalData.licenseType,
            activatedAt,
            expiresAt,
            features: this.getFeatures(originalData.licenseType)
        });

        return this.buildLicense({ licenseType: originalData.licenseType, activatedAt, expiresAt }, user, code);
    }

    // 🏗️ 构建许可证
    private static buildLicense(data: any, user: any, code: string): LicenseInfo {
        return {
            type: data.licenseType,
            userId: user.userId,
            userName: user.userName,
            activatedAt: data.activatedAt,
            expiresAt: data.expiresAt,
            code: code,
            isValid: true,
            features: this.getFeatures(data.licenseType),
            lastCheck: Date.now()
        };
    }

    // 💾 保存许可证
    private static async save(license: LicenseInfo, plugin: any): Promise<void> {
        const signature = this.generateSignature(license);
        const licenseWithSignature = { ...license, signature };
        const encrypted = await this.encrypt(JSON.stringify(licenseWithSignature));
        await plugin.saveData(this.LICENSE_FILE, encrypted, 2);
    }

    // 🗑️ 清除许可证
    static async clear(plugin: any): Promise<void> {
        await plugin.saveData(this.LICENSE_FILE, null, 2);
    }

    // 🔐 加密解密 - 极简版
    private static async encrypt(data: string): Promise<string> {
        const key = await crypto.subtle.digest('SHA-256', new TextEncoder().encode('siyuan-media-player'));
        const iv = crypto.getRandomValues(new Uint8Array(16));
        const keyObj = await crypto.subtle.importKey('raw', key, 'AES-GCM', false, ['encrypt']);
        const encrypted = await crypto.subtle.encrypt({ name: 'AES-GCM', iv }, keyObj, new TextEncoder().encode(data));
        
        const combined = new Uint8Array(iv.length + encrypted.byteLength);
        combined.set(iv);
        combined.set(new Uint8Array(encrypted), iv.length);
        return this.arrayBufferToBase64(combined);
    }

    private static async decrypt(encryptedData: string): Promise<string> {
        const combined = this.base64ToArrayBuffer(encryptedData);
        const iv = combined.slice(0, 16);
        const encrypted = combined.slice(16);
        
        const key = await crypto.subtle.digest('SHA-256', new TextEncoder().encode('siyuan-media-player'));
        const keyObj = await crypto.subtle.importKey('raw', key, 'AES-GCM', false, ['decrypt']);
        const decrypted = await crypto.subtle.decrypt({ name: 'AES-GCM', iv }, keyObj, encrypted);
        return new TextDecoder().decode(decrypted);
    }

    // Base64编码/解码 - 极简版
    private static arrayBufferToBase64(buffer: Uint8Array): string {
        return btoa(String.fromCharCode(...buffer));
    }

    private static base64ToArrayBuffer(base64: string): Uint8Array {
        const binary = atob(base64.replace(/[^A-Za-z0-9+/=]/g, ''));
        return new Uint8Array([...binary].map(char => char.charCodeAt(0)));
    }

    // 🔍 工具函数 - 极简版
    private static generateSignature(license: LicenseInfo): string {
        const { isValid, ...data } = license;
        return btoa(JSON.stringify(data) + 'salt').slice(0, 32);
    }

    private static verifySignature(license: any): boolean {
        if (!license.signature) return false;
        const { signature, ...data } = license;
        return this.generateSignature(data) === signature;
    }

    private static getFeatures(type: string): string[] {
        const features = {
            trial: ['basic_playback'],
            annual: ['cloud_sync', 'batch_import', 'standard_support'],
            dragon: ['cloud_sync', 'batch_import', 'priority_support', 'advanced_features']
        };
        return features[type] || features.trial;
    }

    // S3操作 - 使用思源代理 + AWS4签名
    private static async downloadS3(path: string): Promise<any> {
        const url = `https://${this.S3_CONFIG.bucket}.s3.${this.S3_CONFIG.region}.qiniucs.com/${path}`;
        const headers = await this.generateS3Headers('GET', path);
        
        const response = await fetch('/api/network/forwardProxy', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                url,
                method: 'GET',
                timeout: 10000,
                headers: headers.map(([key, value]) => ({ [key]: value }))
            })
        });

        const result = await response.json();
        if (result.code !== 0) throw new Error(`S3下载失败: ${result.msg}`);
        return await this.decryptS3Data(result.data.body);
    }

    private static async renameS3(oldPath: string, newPath: string, data: any): Promise<void> {
        const encryptedData = await this.encryptS3Data(data);

        // 上传新文件
        const uploadUrl = `https://${this.S3_CONFIG.bucket}.s3.${this.S3_CONFIG.region}.qiniucs.com/${newPath}`;
        const uploadHeaders = await this.generateS3Headers('PUT', newPath);
        uploadHeaders.push(['Content-Type', 'application/json']);
        
        const uploadResponse = await fetch('/api/network/forwardProxy', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                url: uploadUrl,
                method: 'PUT',
                timeout: 15000,
                headers: uploadHeaders.map(([key, value]) => ({ [key]: value })),
                payload: encryptedData
            })
        });

        const uploadResult = await uploadResponse.json();
        if (uploadResult.code !== 0) throw new Error(`S3上传失败: ${uploadResult.msg}`);

        // 删除原文件
        const deleteUrl = `https://${this.S3_CONFIG.bucket}.s3.${this.S3_CONFIG.region}.qiniucs.com/${oldPath}`;
        const deleteHeaders = await this.generateS3Headers('DELETE', oldPath);
        
        const deleteResponse = await fetch('/api/network/forwardProxy', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                url: deleteUrl,
                method: 'DELETE',
                timeout: 10000,
                headers: deleteHeaders.map(([key, value]) => ({ [key]: value }))
            })
        });

        const deleteResult = await deleteResponse.json();
        if (deleteResult.code !== 0) {
            console.warn(`S3删除失败: ${deleteResult.msg}`);
        }
    }

    // AWS4签名 - 极简版
    private static async generateS3Headers(method: string, path: string): Promise<[string, string][]> {
        const now = new Date();
        const dateStamp = now.toISOString().slice(0, 10).replace(/-/g, '');
        const amzDate = now.toISOString().replace(/[:\-]|\.\d{3}/g, '');

        const canonicalUri = `/${path}`;
        const canonicalHeaders = `host:${this.S3_CONFIG.bucket}.s3.${this.S3_CONFIG.region}.qiniucs.com\nx-amz-content-sha256:e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\nx-amz-date:${amzDate}\n`;
        const signedHeaders = 'host;x-amz-content-sha256;x-amz-date';

        const canonicalRequest = `${method}\n${canonicalUri}\n\n${canonicalHeaders}\n${signedHeaders}\ne3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855`;

        const algorithm = 'AWS4-HMAC-SHA256';
        const credentialScope = `${dateStamp}/${this.S3_CONFIG.region}/s3/aws4_request`;
        const stringToSign = `${algorithm}\n${amzDate}\n${credentialScope}\n${await this.sha256(canonicalRequest)}`;

        const signingKey = await this.getSignatureKey(this.S3_CONFIG.secretKey, dateStamp, this.S3_CONFIG.region, 's3');
        const signature = await this.hmacSha256(signingKey, stringToSign);

        const authorizationHeader = `${algorithm} Credential=${this.S3_CONFIG.accessKey}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;

        return [
            ['Authorization', authorizationHeader],
            ['x-amz-content-sha256', 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855'],
            ['x-amz-date', amzDate]
        ];
    }

    private static async sha256(message: string): Promise<string> {
        const msgBuffer = new TextEncoder().encode(message);
        const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer);
        return Array.from(new Uint8Array(hashBuffer)).map(b => b.toString(16).padStart(2, '0')).join('');
    }

    private static async hmacSha256(key: Uint8Array, message: string): Promise<string> {
        const cryptoKey = await crypto.subtle.importKey('raw', key, { name: 'HMAC', hash: 'SHA-256' }, false, ['sign']);
        const signature = await crypto.subtle.sign('HMAC', cryptoKey, new TextEncoder().encode(message));
        return Array.from(new Uint8Array(signature)).map(b => b.toString(16).padStart(2, '0')).join('');
    }

    private static async getSignatureKey(key: string, dateStamp: string, regionName: string, serviceName: string): Promise<Uint8Array> {
        const kDate = await this.hmacSha256Raw(new TextEncoder().encode('AWS4' + key), dateStamp);
        const kRegion = await this.hmacSha256Raw(kDate, regionName);
        const kService = await this.hmacSha256Raw(kRegion, serviceName);
        return await this.hmacSha256Raw(kService, 'aws4_request');
    }

    private static async hmacSha256Raw(key: Uint8Array, message: string): Promise<Uint8Array> {
        const cryptoKey = await crypto.subtle.importKey('raw', key, { name: 'HMAC', hash: 'SHA-256' }, false, ['sign']);
        const signature = await crypto.subtle.sign('HMAC', cryptoKey, new TextEncoder().encode(message));
        return new Uint8Array(signature);
    }

    // S3数据加密解密 - 极简版
    private static async encryptS3Data(data: any): Promise<string> {
        const jsonData = JSON.stringify(data);
        const salt = crypto.getRandomValues(new Uint8Array(16));
        const iv = crypto.getRandomValues(new Uint8Array(16));

        const keyMaterial = await crypto.subtle.importKey('raw', new TextEncoder().encode(this.S3_CONFIG.encryptionKey), 'PBKDF2', false, ['deriveKey']);
        const key = await crypto.subtle.deriveKey({ name: 'PBKDF2', salt: salt, iterations: 10000, hash: 'SHA-256' }, keyMaterial, { name: 'AES-CBC', length: 256 }, false, ['encrypt']);
        const encrypted = await crypto.subtle.encrypt({ name: 'AES-CBC', iv: iv }, key, new TextEncoder().encode(jsonData));

        return JSON.stringify({
            data: btoa(String.fromCharCode(...new Uint8Array(encrypted))),
            salt: btoa(String.fromCharCode(...salt)),
            iv: btoa(String.fromCharCode(...iv))
        });
    }

    private static async decryptS3Data(encryptedDataStr: string): Promise<any> {
        const encryptedData = JSON.parse(encryptedDataStr);
        const salt = Uint8Array.from(atob(encryptedData.salt), c => c.charCodeAt(0));
        const iv = Uint8Array.from(atob(encryptedData.iv), c => c.charCodeAt(0));

        const keyMaterial = await crypto.subtle.importKey('raw', new TextEncoder().encode(this.S3_CONFIG.encryptionKey), 'PBKDF2', false, ['deriveKey']);
        const key = await crypto.subtle.deriveKey({ name: 'PBKDF2', salt: salt, iterations: 10000, hash: 'SHA-256' }, keyMaterial, { name: 'AES-CBC', length: 256 }, false, ['decrypt']);
        const encryptedBytes = Uint8Array.from(atob(encryptedData.data), c => c.charCodeAt(0));
        const decrypted = await crypto.subtle.decrypt({ name: 'AES-CBC', iv: iv }, key, encryptedBytes);

        // 智能处理gzip解压缩
        const decryptedArray = new Uint8Array(decrypted);
        const isGzipped = decryptedArray.length >= 2 && decryptedArray[0] === 0x1f && decryptedArray[1] === 0x8b;

        if (isGzipped) {
            const decompressedStream = new DecompressionStream('gzip');
            const writer = decompressedStream.writable.getWriter();
            const reader = decompressedStream.readable.getReader();

            writer.write(decryptedArray);
            writer.close();

            const chunks = [];
            let done = false;
            while (!done) {
                const { value, done: readerDone } = await reader.read();
                done = readerDone;
                if (value) chunks.push(value);
            }

            const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
            const decompressed = new Uint8Array(totalLength);
            let offset = 0;
            for (const chunk of chunks) {
                decompressed.set(chunk, offset);
                offset += chunk.length;
            }

            return JSON.parse(new TextDecoder().decode(decompressed));
        } else {
            return JSON.parse(new TextDecoder().decode(decryptedArray));
        }
    }

    // 🔍 获取思源用户信息 - 极简版
    static async getSiYuanUserInfo(): Promise<{ userId: string; userName: string } | null> {
        try {
            if ((window as any).siyuan?.user?.userId) {
                return {
                    userId: (window as any).siyuan.user.userId,
                    userName: (window as any).siyuan.user.userName || 'Unknown'
                };
            }

            const response = await fetch('/api/system/getConf');
            if (response.status === 200) {
                const text = await response.text();
                if (text.trim()) {
                    const data = JSON.parse(text);
                    if (data.code === 0 && data.data?.user) {
                        return {
                            userId: data.data.user.userId,
                            userName: data.data.user.userName
                        };
                    }
                }
            }
            return null;
        } catch (error) {
            return null;
        }
    }
}
