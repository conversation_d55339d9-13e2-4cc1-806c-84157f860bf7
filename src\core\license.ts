/**
 * 极简许可证管理器 - 优雅完美版
 * 简洁高效，优雅完美
 */

import { S3Client, GetObjectCommand, PutObjectCommand, DeleteObjectCommand, CopyObjectCommand } from '@aws-sdk/client-s3';

export interface LicenseInfo {
    code?: string;
    userId: string;
    userName: string;
    activatedAt: number;
    expiresAt: number;
    type: 'dragon' | 'annual' | 'trial';
    isValid: boolean;
    features: string[];
}

// 极简许可证结构
interface License {
    type: 'trial' | 'annual' | 'dragon';
    userId: string;
    userName: string;
    activatedAt: number;
    expiresAt: number;      // 0 = 永久
    code?: string;          // 激活码（体验会员无）
    s3Path?: string;        // S3路径（体验会员无）
    lastCheck: number;      // 最后检查时间
    signature: string;      // 防篡改签名
}

export class LicenseManager {
    private static readonly LICENSE_FILE = 'license';
    private static readonly CHECK_INTERVAL = 24 * 60 * 60 * 1000; // 24小时

    // S3配置
    private static readonly S3_CONFIG = {
        accessKey: 'kFCOCF3QVYUcXHk75A5SC9VLPkYzVZZHPq2oApnk',
        secretKey: 'w5YsfJLlrJ3FJdafTq35ht5JYcc38svuDS3YI2Xp',
        bucket: 'siyuan-mediaplayer',
        region: 'cn-south-1',
        endpoint: 'https://s3.cn-south-1.qiniucs.com',
        encryptionKey: 'SiYuan_Fixed_Master_Key_2024_Secure_Change_This'
    };

    // 🎯 主入口 - 极简版
    static async toggle(code: string = '', plugin: any): Promise<{
        success: boolean;
        license?: LicenseInfo;
        message?: string;
        error?: string;
    }> {
        try {
            // 1. 加载现有许可证
            const existing = await this.load(plugin);
            
            // 2. 验证现有许可证
            if (existing && await this.validate(existing)) {
                return { 
                    success: true, 
                    license: this.toLicenseInfo(existing), 
                    message: '许可证有效' 
                };
            }

            // 3. 清除无效许可证
            if (existing) await this.clear(plugin);

            // 4. 创建新许可证
            const newLicense = await this.create(code.trim(), plugin);
            
            // 5. 保存并返回
            await this.save(newLicense, plugin);
            return { 
                success: true, 
                license: this.toLicenseInfo(newLicense), 
                message: newLicense.type === 'trial' ? '体验会员激活成功' : '激活成功'
            };

        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // 📖 加载许可证
    private static async load(plugin: any): Promise<License | null> {
        try {
            const encrypted = await plugin.loadData(this.LICENSE_FILE);
            if (!encrypted) return null;

            const decrypted = await this.decrypt(encrypted);
            const license: License = JSON.parse(decrypted);

            // 验证签名
            if (!await this.verifySignature(license)) {
                console.warn('许可证签名无效');
                return null;
            }

            return license;
        } catch (error) {
            console.warn('加载许可证失败:', error.message);
            return null;
        }
    }

    // ✅ 验证许可证
    private static async validate(license: License): Promise<boolean> {
        try {
            // 1. 检查过期
            if (license.expiresAt > 0 && license.expiresAt < Date.now()) {
                return false;
            }

            // 2. 检查用户（仅正式许可证）
            if (license.type !== 'trial') {
                const user = await this.getSiYuanUserInfo();
                if (!user || user.userId !== license.userId) {
                    return false;
                }
            }

            // 3. 检查S3（仅正式许可证且需要时）
            if (license.type !== 'trial' && this.needsS3Check(license)) {
                const s3Valid = await this.checkS3(license.s3Path!);
                if (!s3Valid) return false;

                // 更新检查时间
                license.lastCheck = Date.now();
                license.signature = await this.generateSignature(license);
            }

            return true;
        } catch (error) {
            console.warn('验证失败:', error.message);
            return false;
        }
    }

    // 🆕 创建许可证
    private static async create(code: string, plugin: any): Promise<License> {
        const user = await this.getSiYuanUserInfo();
        
        if (!code) {
            // 创建体验会员
            return {
                type: 'trial',
                userId: user?.userId || 'guest',
                userName: user?.userName || '游客',
                activatedAt: Date.now(),
                expiresAt: Date.now() + (7 * 24 * 60 * 60 * 1000),
                lastCheck: Date.now(),
                signature: ''
            };
        }

        if (!user) throw new Error('请先登录思源账号');

        // 激活码流程
        const cleanUserName = user.userName.replace(/[^\w\u4e00-\u9fa5]/g, '');
        const userPath = `used/${code}_${user.userId}_${cleanUserName}`;
        
        // 检查用户专属文件
        if (await this.checkS3(userPath)) {
            const data = await this.downloadS3(userPath);
            return {
                type: data.licenseType,
                userId: user.userId,
                userName: user.userName,
                activatedAt: data.activatedAt,
                expiresAt: data.expiresAt,
                code: code,
                s3Path: userPath,
                lastCheck: Date.now(),
                signature: ''
            };
        }

        // 检查原始激活码
        const originalPath = `codes/${code}`;
        if (!await this.checkS3(originalPath)) {
            throw new Error('激活码不存在或已被使用');
        }

        const originalData = await this.downloadS3(originalPath);
        const activatedAt = Date.now();
        const expiresAt = originalData.licenseType === 'dragon' ? 0 : 
                         activatedAt + (365 * 24 * 60 * 60 * 1000);

        // 重命名S3文件
        await this.renameS3(originalPath, userPath, {
            licenseType: originalData.licenseType,
            activatedAt,
            expiresAt,
            features: this.getFeatures(originalData.licenseType)
        });

        return {
            type: originalData.licenseType,
            userId: user.userId,
            userName: user.userName,
            activatedAt,
            expiresAt,
            code: code,
            s3Path: userPath,
            lastCheck: Date.now(),
            signature: ''
        };
    }

    // 💾 保存许可证
    private static async save(license: License, plugin: any): Promise<void> {
        license.signature = await this.generateSignature(license);
        const encrypted = await this.encrypt(JSON.stringify(license));
        await plugin.saveData(this.LICENSE_FILE, encrypted, 2);
    }

    // 🗑️ 清除许可证
    private static async clear(plugin: any): Promise<void> {
        await plugin.saveData(this.LICENSE_FILE, null, 2);
    }

    // 🔄 转换为LicenseInfo
    private static toLicenseInfo(license: License): LicenseInfo {
        return {
            code: license.code,
            userId: license.userId,
            userName: license.userName,
            activatedAt: license.activatedAt,
            expiresAt: license.expiresAt,
            type: license.type,
            isValid: true,
            features: this.getFeatures(license.type)
        };
    }

    // 🔐 加密解密（极简版）
    private static async encrypt(data: string): Promise<string> {
        const key = await crypto.subtle.digest('SHA-256', new TextEncoder().encode('siyuan-media-player'));
        const iv = crypto.getRandomValues(new Uint8Array(16));
        const keyObj = await crypto.subtle.importKey('raw', key, 'AES-GCM', false, ['encrypt']);
        const encrypted = await crypto.subtle.encrypt({ name: 'AES-GCM', iv }, keyObj, new TextEncoder().encode(data));
        
        const combined = new Uint8Array(iv.length + encrypted.byteLength);
        combined.set(iv);
        combined.set(new Uint8Array(encrypted), iv.length);
        return btoa(String.fromCharCode(...combined));
    }

    private static async decrypt(encryptedData: string): Promise<string> {
        const combined = Uint8Array.from(atob(encryptedData), c => c.charCodeAt(0));
        const iv = combined.slice(0, 16);
        const encrypted = combined.slice(16);
        
        const key = await crypto.subtle.digest('SHA-256', new TextEncoder().encode('siyuan-media-player'));
        const keyObj = await crypto.subtle.importKey('raw', key, 'AES-GCM', false, ['decrypt']);
        const decrypted = await crypto.subtle.decrypt({ name: 'AES-GCM', iv }, keyObj, encrypted);
        return new TextDecoder().decode(decrypted);
    }

    // 🔍 工具函数
    private static needsS3Check(license: License): boolean {
        return Date.now() - license.lastCheck > this.CHECK_INTERVAL;
    }

    private static async generateSignature(license: License): Promise<string> {
        const { signature, ...data } = license;
        const hash = await crypto.subtle.digest('SHA-256', new TextEncoder().encode(JSON.stringify(data) + 'salt'));
        return btoa(String.fromCharCode(...new Uint8Array(hash))).slice(0, 32);
    }

    private static async verifySignature(license: License): Promise<boolean> {
        const expected = await this.generateSignature(license);
        return expected === license.signature;
    }

    private static getFeatures(type: string): string[] {
        const features = {
            trial: ['basic_playback'],
            annual: ['cloud_sync', 'batch_import', 'standard_support'],
            dragon: ['cloud_sync', 'batch_import', 'priority_support', 'advanced_features']
        };
        return features[type] || features.trial;
    }

    // S3操作
    private static createS3Client(): S3Client {
        return new S3Client({
            region: this.S3_CONFIG.region,
            endpoint: this.S3_CONFIG.endpoint,
            credentials: {
                accessKeyId: this.S3_CONFIG.accessKey,
                secretAccessKey: this.S3_CONFIG.secretKey
            }
        });
    }

    private static async checkS3(path: string): Promise<boolean> {
        try {
            const client = this.createS3Client();
            const command = new GetObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: path
            });
            await client.send(command);
            return true;
        } catch (error) {
            return false;
        }
    }

    private static async downloadS3(path: string): Promise<any> {
        try {
            const client = this.createS3Client();
            const command = new GetObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: path
            });
            const response = await client.send(command);
            const data = await response.Body?.transformToString();
            return await this.decryptS3Data(data!);
        } catch (error) {
            throw new Error(`下载S3文件失败: ${error.message}`);
        }
    }

    private static async renameS3(oldPath: string, newPath: string, data: any): Promise<void> {
        try {
            const client = this.createS3Client();
            const encryptedData = await this.encryptS3Data(data);

            // 上传新文件
            const putCommand = new PutObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: newPath,
                Body: encryptedData,
                ContentType: 'application/json'
            });
            await client.send(putCommand);

            // 删除原文件
            const deleteCommand = new DeleteObjectCommand({
                Bucket: this.S3_CONFIG.bucket,
                Key: oldPath
            });
            await client.send(deleteCommand);
        } catch (error) {
            throw new Error(`重命名S3文件失败: ${error.message}`);
        }
    }

    private static async encryptS3Data(data: any): Promise<string> {
        const jsonData = JSON.stringify(data);
        const salt = crypto.getRandomValues(new Uint8Array(16));
        const iv = crypto.getRandomValues(new Uint8Array(16));

        const keyMaterial = await crypto.subtle.importKey(
            'raw',
            new TextEncoder().encode(this.S3_CONFIG.encryptionKey),
            'PBKDF2',
            false,
            ['deriveKey']
        );

        const key = await crypto.subtle.deriveKey(
            {
                name: 'PBKDF2',
                salt: salt,
                iterations: 10000,
                hash: 'SHA-256'
            },
            keyMaterial,
            { name: 'AES-CBC', length: 256 },
            false,
            ['encrypt']
        );

        const encrypted = await crypto.subtle.encrypt(
            { name: 'AES-CBC', iv: iv },
            key,
            new TextEncoder().encode(jsonData)
        );

        return JSON.stringify({
            data: btoa(String.fromCharCode(...new Uint8Array(encrypted))),
            salt: btoa(String.fromCharCode(...salt)),
            iv: btoa(String.fromCharCode(...iv))
        });
    }

    private static async decryptS3Data(encryptedDataStr: string): Promise<any> {
        try {
            const encryptedData = JSON.parse(encryptedDataStr);
            const salt = Uint8Array.from(atob(encryptedData.salt), c => c.charCodeAt(0));
            const iv = Uint8Array.from(atob(encryptedData.iv), c => c.charCodeAt(0));

            const keyMaterial = await crypto.subtle.importKey(
                'raw',
                new TextEncoder().encode(this.S3_CONFIG.encryptionKey),
                'PBKDF2',
                false,
                ['deriveKey']
            );

            const key = await crypto.subtle.deriveKey(
                {
                    name: 'PBKDF2',
                    salt: salt,
                    iterations: 10000,
                    hash: 'SHA-256'
                },
                keyMaterial,
                { name: 'AES-CBC', length: 256 },
                false,
                ['decrypt']
            );

            const encryptedBytes = Uint8Array.from(atob(encryptedData.data), c => c.charCodeAt(0));
            const decrypted = await crypto.subtle.decrypt(
                { name: 'AES-CBC', iv: iv },
                key,
                encryptedBytes
            );

            // 处理gzip解压缩
            try {
                const decompressedStream = new DecompressionStream('gzip');
                const writer = decompressedStream.writable.getWriter();
                const reader = decompressedStream.readable.getReader();

                writer.write(new Uint8Array(decrypted));
                writer.close();

                const chunks = [];
                let done = false;
                while (!done) {
                    const { value, done: readerDone } = await reader.read();
                    done = readerDone;
                    if (value) chunks.push(value);
                }

                const decompressed = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));
                let offset = 0;
                for (const chunk of chunks) {
                    decompressed.set(chunk, offset);
                    offset += chunk.length;
                }

                const decryptedText = new TextDecoder().decode(decompressed);
                return JSON.parse(decryptedText);
            } catch (gzipError) {
                const decryptedText = new TextDecoder().decode(decrypted);
                return JSON.parse(decryptedText);
            }
        } catch (error) {
            throw new Error(`S3数据解密失败: ${error.message}`);
        }
    }

    private static async getSiYuanUserInfo(): Promise<{ userId: string; userName: string } | null> {
        try {
            const response = await fetch('/api/system/getConf');
            const data = await response.json();
            if (data.code === 0 && data.data.user) {
                return {
                    userId: data.data.user.userId,
                    userName: data.data.user.userName
                };
            }
            return null;
        } catch (error) {
            console.warn('获取用户信息失败:', error);
            return null;
        }
    }

    // 兼容性方法 - 保持与Setting.svelte的接口一致
    static async handleProState(
        enabled: boolean,
        licenseCode: string,
        currentLicenseCode: string | null,
        plugin?: any
    ): Promise<{ success: boolean; message?: string; error?: string; license?: LicenseInfo; newLicenseCode?: string }> {
        if (!enabled) {
            await this.clear(plugin);
            return { success: true, message: 'Pro功能已关闭' };
        }

        const result = await this.toggle(licenseCode, plugin);
        return {
            success: result.success,
            message: result.message,
            error: result.error,
            license: result.license,
            newLicenseCode: result.license?.code || licenseCode
        };
    }

    static async getCurrentLicense(licenseCode: string | null, plugin?: any): Promise<LicenseInfo | null> {
        const existing = await this.load(plugin);
        if (existing && await this.validate(existing)) {
            return this.toLicenseInfo(existing);
        }
        return null;
    }
}
