/**
 * 🚀 极限精简许可证管理器
 * 极限精简，简洁高效，优雅完美
 */

// 不再需要S3客户端，使用预签名URL

export interface LicenseInfo {
    type: 'trial' | 'annual' | 'dragon';
    userId: string;
    userName: string;
    activatedAt: number;
    expiresAt: number;
    code?: string;
    isValid: boolean;
    features: string[];
    lastCheck: number;
}

export class LicenseManager {
    private static readonly LICENSE_FILE = 'license';
    private static readonly CHECK_INTERVAL = 24 * 60 * 60 * 1000;
    private static readonly ENCRYPTION_KEY = 'SiYuan_Fixed_Master_Key_2024_Secure_Change_This';

    // 🔐 动态解密的S3配置
    private static getS3Config() {
        const encoded = 'a0ZDT0NGM1FWWVVjWEhrNzVBNVNDOVZMUGtZelZaWkhQcTJvQXBua3x3NVlzZkpMbHJKM0ZKZGFmVHEzNWh0NUpZY2MzOHN2dURTM1lJMlhwfHNpeXVhbi1tZWRpYXBsYXllcnxjbi1zb3V0aC0xfGh0dHBzOi8vczMuY24tc291dGgtMS5xaW5pdWNzLmNvbQ==';
        const decoded = atob(encoded);
        const [accessKey, secretKey, bucket, region, endpoint] = decoded.split('|');
        return { accessKey, secretKey, bucket, region, endpoint };
    }

    // 🎯 唯一入口
    static async activate(code = '', plugin: any) {
        try {
            if (code.trim()) {
                const license = await this.activateWithCode(code.trim(), plugin);
                await this.save(license, plugin);
                return { success: true, license, message: '激活成功' };
            }

            const existing = await this.load(plugin);
            if (existing && this.isValid(existing)) {
                return { success: true, license: existing, message: '许可证有效' };
            }

            if (existing) await this.clear(plugin);
            const license = await this.createTrial();
            await this.save(license, plugin);
            return { success: true, license, message: '体验会员激活成功' };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // 📖 加载许可证
    static async load(plugin: any): Promise<LicenseInfo | null> {
        try {
            const encrypted = await plugin.loadData(this.LICENSE_FILE);
            if (!encrypted) return null;
            const decrypted = await this.decrypt(encrypted);
            const license: LicenseInfo = JSON.parse(decrypted);
            return this.verifySignature(license) ? license : null;
        } catch (error) {
            await this.clear(plugin);
            return null;
        }
    }

    // ✅ 验证有效性
    private static isValid(license: LicenseInfo): boolean {
        if (license.expiresAt > 0 && license.expiresAt < Date.now()) return false;
        if (license.type !== 'trial' && this.needsOnlineCheck(license)) return false;
        return true;
    }

    // 🆕 创建体验会员
    private static async createTrial(): Promise<LicenseInfo> {
        const user = await this.getSiYuanUserInfo();
        return {
            type: 'trial',
            userId: user?.userId || 'guest',
            userName: user?.userName || '游客',
            activatedAt: Date.now(),
            expiresAt: Date.now() + (7 * 24 * 60 * 60 * 1000),
            isValid: true,
            features: ['basic_playback'],
            lastCheck: Date.now()
        };
    }

    // 🔥 激活码激活
    private static async activateWithCode(code: string, plugin: any): Promise<LicenseInfo> {
        const user = await this.getSiYuanUserInfo();
        if (!user) throw new Error('请先登录思源账号');

        const existing = await this.load(plugin);
        const cleanUserName = user.userName.replace(/[^\w\u4e00-\u9fa5]/g, '');
        const userPath = `${code}_${user.userId}_${cleanUserName}.dat`;

        // 智能选择查询策略
        if (!existing || existing.type === 'trial') {
            // 无许可证或体验许可证 → 直接查询原始激活码
            return await this.activateFromOriginal(code, user, cleanUserName);
        } else {
            // 正式许可证 → 先查询用户专属文件
            try {
                const data = await this.downloadS3(userPath);
                return this.buildLicense(data, user, code);
            } catch (error) {
                return await this.activateFromOriginal(code, user, cleanUserName);
            }
        }
    }

    // 🔥 从原始激活码激活
    private static async activateFromOriginal(code: string, user: any, cleanUserName: string): Promise<LicenseInfo> {
        const originalData = await this.downloadS3(`${code}.dat`);
        const activatedAt = Date.now();
        const expiresAt = originalData.licenseType === 'dragon' ? 0 : activatedAt + (365 * 24 * 60 * 60 * 1000);

        const userPath = `${code}_${user.userId}_${cleanUserName}.dat`;
        await this.renameS3(`${code}.dat`, userPath, {
            licenseType: originalData.licenseType,
            activatedAt,
            expiresAt,
            features: this.getFeatures(originalData.licenseType)
        });

        return this.buildLicense({ licenseType: originalData.licenseType, activatedAt, expiresAt }, user, code);
    }

    // 🏗️ 构建许可证
    private static buildLicense(data: any, user: any, code: string): LicenseInfo {
        return {
            type: data.licenseType,
            userId: user.userId,
            userName: user.userName,
            activatedAt: data.activatedAt,
            expiresAt: data.expiresAt,
            code: code,
            isValid: true,
            features: this.getFeatures(data.licenseType),
            lastCheck: Date.now()
        };
    }

    // 💾 保存许可证
    private static async save(license: LicenseInfo, plugin: any): Promise<void> {
        const signature = this.generateSignature(license);
        const licenseWithSignature = { ...license, signature };
        const encrypted = await this.encrypt(JSON.stringify(licenseWithSignature));
        await plugin.saveData(this.LICENSE_FILE, encrypted, 2);
    }

    // 🗑️ 清除许可证
    static async clear(plugin: any): Promise<void> {
        await plugin.saveData(this.LICENSE_FILE, null, 2);
    }

    // 🔐 加密解密
    private static async encrypt(data: string): Promise<string> {
        const key = await crypto.subtle.digest('SHA-256', new TextEncoder().encode('siyuan-media-player'));
        const iv = crypto.getRandomValues(new Uint8Array(16));
        const keyObj = await crypto.subtle.importKey('raw', key, 'AES-GCM', false, ['encrypt']);
        const encrypted = await crypto.subtle.encrypt({ name: 'AES-GCM', iv }, keyObj, new TextEncoder().encode(data));
        
        const combined = new Uint8Array(iv.length + encrypted.byteLength);
        combined.set(iv);
        combined.set(new Uint8Array(encrypted), iv.length);
        return this.arrayBufferToBase64(combined);
    }

    private static async decrypt(encryptedData: string): Promise<string> {
        const combined = this.base64ToArrayBuffer(encryptedData);
        const iv = combined.slice(0, 16);
        const encrypted = combined.slice(16);
        
        const key = await crypto.subtle.digest('SHA-256', new TextEncoder().encode('siyuan-media-player'));
        const keyObj = await crypto.subtle.importKey('raw', key, 'AES-GCM', false, ['decrypt']);
        const decrypted = await crypto.subtle.decrypt({ name: 'AES-GCM', iv }, keyObj, encrypted);
        return new TextDecoder().decode(decrypted);
    }

    // Base64编码/解码
    private static arrayBufferToBase64(buffer: Uint8Array): string {
        let binary = '';
        for (let i = 0; i < buffer.length; i++) {
            binary += String.fromCharCode(buffer[i]);
        }
        return btoa(binary);
    }

    private static base64ToArrayBuffer(base64: string): Uint8Array {
        const cleanBase64 = base64.replace(/[^A-Za-z0-9+/=]/g, '');
        const binary = atob(cleanBase64);
        const bytes = new Uint8Array(binary.length);
        for (let i = 0; i < binary.length; i++) {
            bytes[i] = binary.charCodeAt(i);
        }
        return bytes;
    }

    // 🔍 工具函数
    private static needsOnlineCheck(license: LicenseInfo): boolean {
        return Date.now() - license.lastCheck > this.CHECK_INTERVAL;
    }

    private static generateSignature(license: LicenseInfo): string {
        const { isValid, ...data } = license;
        const hash = JSON.stringify(data) + 'salt';
        return btoa(hash).slice(0, 32);
    }

    private static verifySignature(license: any): boolean {
        if (!license.signature) return false;
        const { signature, ...data } = license;
        const expected = this.generateSignature(data);
        return expected === signature;
    }

    private static getFeatures(type: string): string[] {
        const features = {
            trial: ['basic_playback'],
            annual: ['cloud_sync', 'batch_import', 'standard_support'],
            dragon: ['cloud_sync', 'batch_import', 'priority_support', 'advanced_features']
        };
        return features[type] || features.trial;
    }

    // 🔗 S3操作 - 前端生成预签名URL（无需后端）
    private static async downloadS3(fileName: string): Promise<any> {
        try {
            // 1. 前端直接生成预签名URL
            const presignedUrl = await this.generatePresignedUrl(fileName, 300);

            // 2. 使用预签名URL下载文件
            const fileResponse = await fetch(presignedUrl);
            if (!fileResponse.ok) {
                throw new Error(`HTTP ${fileResponse.status}`);
            }

            const data = await fileResponse.text();
            return await this.decryptS3Data(data);
        } catch (error) {
            throw new Error(`下载S3文件失败: ${error.message}`);
        }
    }

    // 🔗 前端生成预签名URL - 无需后端
    private static async generatePresignedUrl(fileName: string, expiresIn: number = 300): Promise<string> {
        const config = this.getS3Config();
        const region = config.region;
        const bucket = config.bucket;
        const endpoint = config.endpoint;

        const now = new Date();
        const amzDate = now.toISOString().replace(/[:\-]|\.\d{3}/g, '');
        const dateStamp = amzDate.substring(0, 8);

        const credentialScope = `${dateStamp}/${region}/s3/aws4_request`;
        const credential = `${config.accessKey}/${credentialScope}`;

        const params = new URLSearchParams({
            'X-Amz-Algorithm': 'AWS4-HMAC-SHA256',
            'X-Amz-Credential': credential,
            'X-Amz-Date': amzDate,
            'X-Amz-Expires': expiresIn.toString(),
            'X-Amz-SignedHeaders': 'host'
        });

        const canonicalRequest = [
            'GET',
            `/${fileName}`,
            params.toString(),
            `host:${new URL(endpoint).host}`,
            '',
            'host',
            'UNSIGNED-PAYLOAD'
        ].join('\n');

        const stringToSign = [
            'AWS4-HMAC-SHA256',
            amzDate,
            credentialScope,
            await this.sha256(canonicalRequest)
        ].join('\n');

        const signature = await this.getSignature(config.secretKey, dateStamp, region, stringToSign);
        params.set('X-Amz-Signature', signature);

        return `${endpoint}/${fileName}?${params.toString()}`;
    }

    // 🔐 SHA256哈希
    private static async sha256(message: string): Promise<string> {
        const msgBuffer = new TextEncoder().encode(message);
        const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    }

    // 🔐 AWS4签名
    private static async getSignature(secretKey: string, dateStamp: string, region: string, stringToSign: string): Promise<string> {
        const kDate = await this.hmacSha256(`AWS4${secretKey}`, dateStamp);
        const kRegion = await this.hmacSha256(kDate, region);
        const kService = await this.hmacSha256(kRegion, 's3');
        const kSigning = await this.hmacSha256(kService, 'aws4_request');
        const signature = await this.hmacSha256(kSigning, stringToSign);

        return Array.from(new Uint8Array(signature)).map(b => b.toString(16).padStart(2, '0')).join('');
    }

    // 🔐 HMAC-SHA256
    private static async hmacSha256(key: string | ArrayBuffer, message: string): Promise<ArrayBuffer> {
        const keyBuffer = typeof key === 'string' ? new TextEncoder().encode(key) : key;
        const msgBuffer = new TextEncoder().encode(message);
        const cryptoKey = await crypto.subtle.importKey('raw', keyBuffer, { name: 'HMAC', hash: 'SHA-256' }, false, ['sign']);
        return await crypto.subtle.sign('HMAC', cryptoKey, msgBuffer);
    }

    // 🔄 重命名S3文件 - 前端实现（简化版）
    private static async renameS3(oldPath: string, newPath: string, data: any): Promise<void> {
        // 注意：预签名URL只支持读取，重命名需要写权限
        // 这里我们简化处理，实际使用中激活码重命名由后端管理
        console.log(`S3文件重命名: ${oldPath} → ${newPath}`);
        // 在实际场景中，这个操作会在后端自动完成
    }



    private static async decryptS3Data(encryptedDataStr: string): Promise<any> {
        const encryptedData = JSON.parse(encryptedDataStr);
        const salt = Uint8Array.from(atob(encryptedData.salt), c => c.charCodeAt(0));
        const iv = Uint8Array.from(atob(encryptedData.iv), c => c.charCodeAt(0));
        
        const keyMaterial = await crypto.subtle.importKey('raw', new TextEncoder().encode(this.ENCRYPTION_KEY), 'PBKDF2', false, ['deriveKey']);
        const key = await crypto.subtle.deriveKey({ name: 'PBKDF2', salt: salt, iterations: 10000, hash: 'SHA-256' }, keyMaterial, { name: 'AES-CBC', length: 256 }, false, ['decrypt']);
        const encryptedBytes = Uint8Array.from(atob(encryptedData.data), c => c.charCodeAt(0));
        const decrypted = await crypto.subtle.decrypt({ name: 'AES-CBC', iv: iv }, key, encryptedBytes);
        
        // 智能处理gzip解压缩
        const decryptedArray = new Uint8Array(decrypted);
        const isGzipped = decryptedArray.length >= 2 && decryptedArray[0] === 0x1f && decryptedArray[1] === 0x8b;
        
        if (isGzipped) {
            const decompressedStream = new DecompressionStream('gzip');
            const writer = decompressedStream.writable.getWriter();
            const reader = decompressedStream.readable.getReader();
            
            writer.write(decryptedArray);
            writer.close();
            
            const chunks = [];
            let done = false;
            while (!done) {
                const { value, done: readerDone } = await reader.read();
                done = readerDone;
                if (value) chunks.push(value);
            }
            
            const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
            const decompressed = new Uint8Array(totalLength);
            let offset = 0;
            for (const chunk of chunks) {
                decompressed.set(chunk, offset);
                offset += chunk.length;
            }
            
            return JSON.parse(new TextDecoder().decode(decompressed));
        } else {
            return JSON.parse(new TextDecoder().decode(decryptedArray));
        }
    }

    // 🔍 获取思源用户信息
    static async getSiYuanUserInfo(): Promise<{ userId: string; userName: string } | null> {
        try {
            if ((window as any).siyuan?.user?.userId) {
                return {
                    userId: (window as any).siyuan.user.userId,
                    userName: (window as any).siyuan.user.userName || 'Unknown'
                };
            }
            
            const response = await fetch('/api/system/getConf');
            if (response.status === 200) {
                const text = await response.text();
                if (text.trim()) {
                    const data = JSON.parse(text);
                    if (data.code === 0 && data.data?.user) {
                        return {
                            userId: data.data.user.userId,
                            userName: data.data.user.userName
                        };
                    }
                }
            }
            return null;
        } catch (error) {
            return null;
        }
    }
}
